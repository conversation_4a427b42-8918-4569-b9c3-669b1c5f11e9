1
00:00:00,000 --> 00:00:03,000
foreign

2
00:00:05,120 --> 00:00:11,400
thanks yeah right it's a sort of review

3
00:00:11,400 --> 00:00:15,120
some things we have already seen and

4
00:00:15,120 --> 00:00:19,380
some things come from this paper that uh

5
00:00:19,380 --> 00:00:20,100
that you have seen uh online

6
00:00:20,100 --> 00:00:22,080
um

7
00:00:22,080 --> 00:00:24,539
so

8
00:00:24,539 --> 00:00:27,779
the paper is uh

9
00:00:27,779 --> 00:00:30,779
you have seen it's over 30 pages uh it

10
00:00:30,779 --> 00:00:33,239
has a lot of things in it it's quite

11
00:00:33,239 --> 00:00:35,100
dense in math so we are not going to I'm

12
00:00:35,100 --> 00:00:36,980
not going to present everything that is

13
00:00:36,980 --> 00:00:40,800
in the paper

14
00:00:40,800 --> 00:00:42,660
I'm I'm going to do a kind of uh merge

15
00:00:42,660 --> 00:00:44,640
with some things that we have seen

16
00:00:44,640 --> 00:00:47,460
already some things that we have not

17
00:00:47,460 --> 00:00:49,680
seen already that are like the phases

18
00:00:49,680 --> 00:00:54,739
the fundamentals for doing optimizations

19
00:00:54,739 --> 00:00:58,520
of on algorithms on Matrix manifolds and

20
00:00:58,520 --> 00:01:00,680
and especially from the paper we look at

21
00:01:00,680 --> 00:01:06,000
bfgs

22
00:01:06,000 --> 00:01:09,840
the Romanian version of bfgs and and the

23
00:01:09,840 --> 00:01:14,100
application in of this method in image

24
00:01:14,100 --> 00:01:16,200
segmentation problems uh so yeah so the

25
00:01:16,200 --> 00:01:19,320
main contributions of this paper are

26
00:01:19,320 --> 00:01:22,860
convergence and convergence so converges

27
00:01:22,860 --> 00:01:27,619
results and convergence rates of bfgs

28
00:01:27,619 --> 00:01:30,180
and of uh bfgs for uh

29
00:01:30,180 --> 00:01:31,619
in its Romanian version

30
00:01:31,619 --> 00:01:33,600
and

31
00:01:33,600 --> 00:01:36,060
the same thing so convergence and

32
00:01:36,060 --> 00:01:38,280
convergence rates for the Fletcher

33
00:01:38,280 --> 00:01:40,140
Reeves non-linear conjugate gradient

34
00:01:40,140 --> 00:01:42,479
algorithms that we are not going to see

35
00:01:42,479 --> 00:01:45,420
in this talk and then they have

36
00:01:45,420 --> 00:01:48,780
numerical applications in as I said

37
00:01:48,780 --> 00:01:52,380
image segmentation and also in the space

38
00:01:52,380 --> 00:01:55,200
of trans shape deformations

39
00:01:55,200 --> 00:01:57,659
so yeah so and this is what we are going

40
00:01:57,659 --> 00:02:00,960
to see so some fundamentals for doing

41
00:02:00,960 --> 00:02:03,899
optimization on manifolds uh the

42
00:02:03,899 --> 00:02:07,159
Romanian version of bfgs and application

43
00:02:07,159 --> 00:02:07,159
to image segmentation

44
00:02:07,259 --> 00:02:14,940
okay so we start with a more General

45
00:02:14,940 --> 00:02:19,020
framework so optimization problems uh on

46
00:02:19,020 --> 00:02:21,720
Metric Matrix manifolds uh they read in

47
00:02:21,720 --> 00:02:24,720
general like this so the optimization

48
00:02:24,720 --> 00:02:27,300
problems setting is this one so we want

49
00:02:27,300 --> 00:02:31,800
to minimize an objective function which

50
00:02:31,800 --> 00:02:35,340
is defined from the manifold to to R uh

51
00:02:35,340 --> 00:02:37,739
with with the constraint that the

52
00:02:37,739 --> 00:02:40,440
optimization variable belongs to it is

53
00:02:40,440 --> 00:02:41,580
on the manifold okay it's on some Matrix

54
00:02:41,580 --> 00:02:44,940
manifold

55
00:02:44,940 --> 00:02:47,660
so metrics manifolds uh metrics manifold

56
00:02:47,660 --> 00:02:51,420
is any manifold that is constructed from

57
00:02:51,420 --> 00:02:56,040
the euclidean space in this case we

58
00:02:56,040 --> 00:02:58,560
consider r n times P okay by taking

59
00:02:58,560 --> 00:03:01,220
either embedded sub manifolds or

60
00:03:01,220 --> 00:03:04,080
quotient manifolds

61
00:03:04,080 --> 00:03:06,599
so some examples of embedded some

62
00:03:06,599 --> 00:03:08,459
manifolds there is this default manifold

63
00:03:08,459 --> 00:03:09,959
that we have already the orthogonal

64
00:03:09,959 --> 00:03:13,620
stiffer manifold that we have already

65
00:03:13,620 --> 00:03:15,599
seen in the previous talk and it's going

66
00:03:15,599 --> 00:03:18,480
again to be let's say our running

67
00:03:18,480 --> 00:03:21,780
example in this talk and there are also

68
00:03:21,780 --> 00:03:24,120
other many other examples maybe uh I

69
00:03:24,120 --> 00:03:26,099
don't know later in other talks maybe we

70
00:03:26,099 --> 00:03:27,260
will see the manifold of fixed rank

71
00:03:27,260 --> 00:03:30,659
matrices

72
00:03:30,659 --> 00:03:33,959
for example and for the quotient

73
00:03:33,959 --> 00:03:35,940
manifold the most important instance is

74
00:03:35,940 --> 00:03:37,980
the Craftsman manifold which maybe we

75
00:03:37,980 --> 00:03:39,720
will also see in some other in some

76
00:03:39,720 --> 00:03:42,659
other talk in the future

77
00:03:42,659 --> 00:03:45,420
okay so why why do we consider this kind

78
00:03:45,420 --> 00:03:48,480
of optimization problems

79
00:03:48,480 --> 00:03:51,900
um the reason is that so In classical

80
00:03:51,900 --> 00:03:55,440
optimization if you want to solve let's

81
00:03:55,440 --> 00:03:57,720
say a similar problem like this uh so

82
00:03:57,720 --> 00:03:59,879
you have you have all methods to to

83
00:03:59,879 --> 00:04:03,360
treat constrained optimization problems

84
00:04:03,360 --> 00:04:07,560
like you use uh lagrangians

85
00:04:07,560 --> 00:04:10,019
Etc in this case what we what we do in

86
00:04:10,019 --> 00:04:13,140
uh optimization problems and Method

87
00:04:13,140 --> 00:04:15,480
metrics manifolds we exploit the

88
00:04:15,480 --> 00:04:17,820
underlying geometric structure of the

89
00:04:17,820 --> 00:04:20,400
problem so the geometric structure so

90
00:04:20,400 --> 00:04:24,360
the manifold the dramatic structure of

91
00:04:24,360 --> 00:04:26,759
the manifold and in this way we are we

92
00:04:26,759 --> 00:04:29,120
can take into account the constraints so

93
00:04:29,120 --> 00:04:31,919
the fact that the optimization variable

94
00:04:31,919 --> 00:04:36,419
belongs to to the manifold explicitly

95
00:04:36,419 --> 00:04:39,060
okay so it's uh and and so

96
00:04:39,060 --> 00:04:41,100
um and this also usually leads to more

97
00:04:41,100 --> 00:04:44,340
efficient algae algorithm so we both

98
00:04:44,340 --> 00:04:47,460
have a structure the geometric structure

99
00:04:47,460 --> 00:04:51,240
that we take into account and this gives

100
00:04:51,240 --> 00:04:54,479
us some more efficiency okay so the main

101
00:04:54,479 --> 00:04:59,340
references for the this first part are

102
00:04:59,340 --> 00:05:02,160
these uh these books okay so like uh

103
00:05:02,160 --> 00:05:05,460
more or less every every 10 years there

104
00:05:05,460 --> 00:05:09,479
is a major like a major reference that

105
00:05:09,479 --> 00:05:12,600
supersedes the previous one so now

106
00:05:12,600 --> 00:05:15,240
usually I make ref usually before I was

107
00:05:15,240 --> 00:05:18,000
making reference to this book and now

108
00:05:18,000 --> 00:05:20,280
since uh this is the newest one so

109
00:05:20,280 --> 00:05:22,560
usually I I look into this book and

110
00:05:22,560 --> 00:05:25,259
there are all the three are a bit

111
00:05:25,259 --> 00:05:28,380
available online so you you will have

112
00:05:28,380 --> 00:05:29,699
the link also in the pdf version of the

113
00:05:29,699 --> 00:05:32,940
slides

114
00:05:32,940 --> 00:05:34,500
so okay so now let's say I will try to

115
00:05:34,500 --> 00:05:36,900
go a bit faster in the first part

116
00:05:36,900 --> 00:05:40,680
because some things we have already seen

117
00:05:40,680 --> 00:05:42,840
so like for example in what what the

118
00:05:42,840 --> 00:05:45,419
about the stiffel manifold we have

119
00:05:45,419 --> 00:05:48,000
already seen that it is a set of

120
00:05:48,000 --> 00:05:50,400
matrices with orthonormal columns so

121
00:05:50,400 --> 00:05:52,800
this is a kind of recap

122
00:05:52,800 --> 00:05:56,340
um we have seen that we have a

123
00:05:56,340 --> 00:05:59,400
formulation of the tangent space uh to

124
00:05:59,400 --> 00:06:01,860
to M at X so in general is the set of

125
00:06:01,860 --> 00:06:05,340
all tangent vectors to a manifold at X

126
00:06:05,340 --> 00:06:08,539
and for the stiffel manifold we can have

127
00:06:08,539 --> 00:06:12,500
this specific form of

128
00:06:12,500 --> 00:06:16,220
of the tangent vectors so we have

129
00:06:16,220 --> 00:06:20,220
at an orthonormal Matrix X

130
00:06:20,220 --> 00:06:24,300
any tangent Vector has this form so it's

131
00:06:24,300 --> 00:06:29,880
x times Omega excuse symmetric Matrix of

132
00:06:29,880 --> 00:06:33,180
size P plus X perp times K where xperp

133
00:06:33,180 --> 00:06:36,960
is an orthonormal Matrix such that the

134
00:06:36,960 --> 00:06:40,139
span of its column so the this Subspace

135
00:06:40,139 --> 00:06:42,419
spanned by its column

136
00:06:42,419 --> 00:06:45,900
um is equal to the orthogonal complement

137
00:06:45,900 --> 00:06:48,020
to the Subspace spanned by The Columns

138
00:06:48,020 --> 00:06:51,300
of X okay

139
00:06:51,300 --> 00:06:54,419
and then K is a matrix on which we have

140
00:06:54,419 --> 00:06:57,840
no specific constraint apart from its

141
00:06:57,840 --> 00:07:00,479
size of course okay so with this with

142
00:07:00,479 --> 00:07:02,400
this format it is it's very easy to

143
00:07:02,400 --> 00:07:04,500
compute the dimension of the manifold

144
00:07:04,500 --> 00:07:08,460
and this we have already seen so in the

145
00:07:08,460 --> 00:07:11,039
end the matrices they are n by P but in

146
00:07:11,039 --> 00:07:13,560
the end uh the dimension of the manifold

147
00:07:13,560 --> 00:07:17,520
is less okay so the degrees of freedom

148
00:07:17,520 --> 00:07:20,340
are less than n times P because there is

149
00:07:20,340 --> 00:07:22,620
some kind of geometric constraint inside

150
00:07:22,620 --> 00:07:26,460
okay

151
00:07:26,460 --> 00:07:29,280
so as we have seen last time to manifold

152
00:07:29,280 --> 00:07:30,620
a Romanian manifold strictly speaking is

153
00:07:30,620 --> 00:07:35,520
a manifold

154
00:07:35,520 --> 00:07:38,900
with a smoothly varying inner product on

155
00:07:38,900 --> 00:07:43,680
it okay which is called Romanian metric

156
00:07:43,680 --> 00:07:46,819
uh yeah classical is denoted by G but we

157
00:07:46,819 --> 00:07:49,500
can we have many other notations

158
00:07:49,500 --> 00:07:53,520
so for this default manifold we have

159
00:07:53,520 --> 00:07:56,520
seen the the two Matrix the two possible

160
00:07:56,520 --> 00:08:01,259
choices so the embedded metric which is

161
00:08:01,259 --> 00:08:04,099
inherited from the inherited by the

162
00:08:04,099 --> 00:08:08,400
stifel manifold from the embedding space

163
00:08:08,400 --> 00:08:10,440
are n times P so it's just an inner

164
00:08:10,440 --> 00:08:12,060
product for matrices which is also

165
00:08:12,060 --> 00:08:15,000
called the frobinus inner product

166
00:08:15,000 --> 00:08:17,880
because it induces the frobenius norm

167
00:08:17,880 --> 00:08:20,099
and the canonical metric is given by

168
00:08:20,099 --> 00:08:22,500
seeing the stiffel manifold as a

169
00:08:22,500 --> 00:08:25,259
quotient of the orthogonal group

170
00:08:25,259 --> 00:08:29,220
so we quotient out all the orthonormal

171
00:08:29,220 --> 00:08:32,099
matrices of size n minus P okay and then

172
00:08:32,099 --> 00:08:35,099
we have this metric this inner product

173
00:08:35,099 --> 00:08:37,500
which differs from the embedded one only

174
00:08:37,500 --> 00:08:41,640
for the presence of this term

175
00:08:41,640 --> 00:08:42,479
uh uh in in yellow and we have seen last

176
00:08:42,479 --> 00:08:44,039
time

177
00:08:44,039 --> 00:08:46,440
that

178
00:08:46,440 --> 00:08:49,860
so if we look at if we want to compute

179
00:08:49,860 --> 00:08:52,680
the length of the tangent Vector uh

180
00:08:52,680 --> 00:08:54,680
so attention Vector that has that format

181
00:08:54,680 --> 00:08:57,779
that we have seen

182
00:08:57,779 --> 00:09:01,019
yeah the only difference in the actual

183
00:09:01,019 --> 00:09:06,480
calculations is the appearance of this

184
00:09:06,480 --> 00:09:08,820
one half Vector here and last time I I

185
00:09:08,820 --> 00:09:12,620
wanted and I forgot to put a concrete

186
00:09:12,620 --> 00:09:16,200
example so for example for p equal to 3

187
00:09:16,200 --> 00:09:19,320
sqc metric Matrix looks like this so

188
00:09:19,320 --> 00:09:21,839
where ABC are just some reals so the the

189
00:09:21,839 --> 00:09:24,240
lower triangular part is just minus the

190
00:09:24,240 --> 00:09:26,580
upper triangular part okay and then if

191
00:09:26,580 --> 00:09:29,760
you do the calculations of the frobinus

192
00:09:29,760 --> 00:09:31,740
norm squared of this Matrix so this part

193
00:09:31,740 --> 00:09:32,820
here you have or you have this

194
00:09:32,820 --> 00:09:35,399
expression

195
00:09:35,399 --> 00:09:38,040
so if you use the embedded metric

196
00:09:38,040 --> 00:09:41,700
embedded metric you have you you count

197
00:09:41,700 --> 00:09:44,220
each coefficient twice and if you use

198
00:09:44,220 --> 00:09:47,700
the canonical metric actually you divide

199
00:09:47,700 --> 00:09:51,120
by two so it is counted only once okay

200
00:09:51,120 --> 00:09:53,459
so that's that's the reason why is as I

201
00:09:53,459 --> 00:09:55,680
was saying last time two is kind of more

202
00:09:55,680 --> 00:09:57,720
Natural Choice let's say it really

203
00:09:57,720 --> 00:10:00,000
considers the fact that

204
00:10:00,000 --> 00:10:02,519
there is some kind of constraint so the

205
00:10:02,519 --> 00:10:03,600
degrees of freedoms are less than n

206
00:10:03,600 --> 00:10:05,519
times p

207
00:10:05,519 --> 00:10:07,390
okay

208
00:10:07,390 --> 00:10:07,740
okay

209
00:10:07,740 --> 00:10:09,120
[Music]

210
00:10:09,120 --> 00:10:12,500
um

211
00:10:12,500 --> 00:10:15,540
okay so I think that we use in in

212
00:10:15,540 --> 00:10:18,360
optimization algorithms something that

213
00:10:18,360 --> 00:10:22,019
we need is to compute the the gradient

214
00:10:22,019 --> 00:10:25,080
in this case the Romanian gradient so uh

215
00:10:25,080 --> 00:10:27,740
I mean you have seen uh

216
00:10:27,740 --> 00:10:31,080
you have for sure seen a classical

217
00:10:31,080 --> 00:10:34,620
optimization algorithms in a euclidean

218
00:10:34,620 --> 00:10:37,500
space in flat space or also maybe also

219
00:10:37,500 --> 00:10:39,240
at the node that I sent you if you had a

220
00:10:39,240 --> 00:10:41,160
look at it so the

221
00:10:41,160 --> 00:10:44,579
if we want to use some first order

222
00:10:44,579 --> 00:10:47,399
information about the cost function like

223
00:10:47,399 --> 00:10:49,800
in uh in steepest descent or gradient

224
00:10:49,800 --> 00:10:52,579
method another name for the same method

225
00:10:52,579 --> 00:10:55,980
we compute the gradient

226
00:10:55,980 --> 00:10:57,779
so okay let F be the objective function

227
00:10:57,779 --> 00:11:01,920
on the manifold in an optimization

228
00:11:01,920 --> 00:11:04,980
problem like defining the second slide

229
00:11:04,980 --> 00:11:06,959
and for any embedded sub manifold like

230
00:11:06,959 --> 00:11:08,940
the Stephen manifold is an embedded sub

231
00:11:08,940 --> 00:11:11,399
manifold the Romanian gradient is just

232
00:11:11,399 --> 00:11:12,620
given by the projection of the euclidean

233
00:11:12,620 --> 00:11:15,839
gradient

234
00:11:15,839 --> 00:11:17,640
onto the tangent space at X

235
00:11:17,640 --> 00:11:19,560
so for this default manifold the

236
00:11:19,560 --> 00:11:22,200
orthogonal it's it's an orthogonal

237
00:11:22,200 --> 00:11:25,380
projection so for the stiffer manifold

238
00:11:25,380 --> 00:11:27,720
the orthogonal projection of any Matrix

239
00:11:27,720 --> 00:11:31,019
a generic Matrix belonging to the

240
00:11:31,019 --> 00:11:34,079
embedding space so a generic Matrix M of

241
00:11:34,079 --> 00:11:36,480
size n times p on the tangent space is

242
00:11:36,480 --> 00:11:40,380
given by this expression okay so you see

243
00:11:40,380 --> 00:11:42,180
we it it repeats like the the form of

244
00:11:42,180 --> 00:11:44,040
the of the tangent Vector right because

245
00:11:44,040 --> 00:11:46,800
here we have X

246
00:11:46,800 --> 00:11:48,660
it multiplies by SQ symmetric Matrix

247
00:11:48,660 --> 00:11:51,180
this is an operator that gives you the

248
00:11:51,180 --> 00:11:53,480
exclusive metric part of this Matrix and

249
00:11:53,480 --> 00:11:55,860
then we have the part in the

250
00:11:55,860 --> 00:11:58,500
orthogonal complement this is a

251
00:11:58,500 --> 00:12:02,339
projection orthogonal projector

252
00:12:02,339 --> 00:12:03,959
and the death Matrix okay

253
00:12:03,959 --> 00:12:08,940
uh

254
00:12:08,940 --> 00:12:11,339
yeah and as I said uh nabla nabla f of x

255
00:12:11,339 --> 00:12:14,279
so the gradient of f of x is the

256
00:12:14,279 --> 00:12:15,959
euclidean gradient of f of x so for

257
00:12:15,959 --> 00:12:18,600
example if you have a matrix function

258
00:12:18,600 --> 00:12:21,540
like this one so this was the function

259
00:12:21,540 --> 00:12:25,260
coming from the talk that I gave on

260
00:12:25,260 --> 00:12:28,440
Federated learning so this is the uh the

261
00:12:28,440 --> 00:12:30,360
kpca objective function yeah okay but

262
00:12:30,360 --> 00:12:32,700
here we just look at it as Matrix

263
00:12:32,700 --> 00:12:35,700
function function of X with some Matrix

264
00:12:35,700 --> 00:12:38,820
a if you compute the euclidean gradient

265
00:12:38,820 --> 00:12:42,000
this is just minus a times x and then

266
00:12:42,000 --> 00:12:44,220
you plug this you insert this into this

267
00:12:44,220 --> 00:12:46,680
and you use the the expression for the

268
00:12:46,680 --> 00:12:49,560
projector and you have the you have the

269
00:12:49,560 --> 00:12:53,779
Romanian gradient on the stiffer

270
00:12:53,779 --> 00:12:53,779
manifold for that objective function

271
00:12:55,260 --> 00:13:00,360
other tools that we did I mean

272
00:13:00,360 --> 00:13:03,180
fundamental Tools in in Romanian

273
00:13:03,180 --> 00:13:05,579
geometry and used in optimization or at

274
00:13:05,579 --> 00:13:07,620
least in in some approximate version are

275
00:13:07,620 --> 00:13:10,440
the Romanian exponential logarithms so

276
00:13:10,440 --> 00:13:15,240
this we have also seen last time so if

277
00:13:15,240 --> 00:13:19,380
we have a point on a manifold and a

278
00:13:19,380 --> 00:13:21,779
tangent Vector to the manifold at X and

279
00:13:21,779 --> 00:13:24,540
Gamma T digiodesic the geodesic

280
00:13:24,540 --> 00:13:27,260
emanating from from that point in the

281
00:13:27,260 --> 00:13:30,180
direction of PSI so

282
00:13:30,180 --> 00:13:33,920
the exponential mapping is the mapping

283
00:13:33,920 --> 00:13:38,880
from the tangent space to M such that

284
00:13:38,880 --> 00:13:42,899
it Maps the the tangent Vector to the to

285
00:13:42,899 --> 00:13:44,339
the point on the judici at t equal to

286
00:13:44,339 --> 00:13:47,459
one so

287
00:13:47,459 --> 00:13:50,959
where T is the parameter is the is the

288
00:13:50,959 --> 00:13:54,060
real that parameterized the geodesic

289
00:13:54,060 --> 00:13:56,700
uh yeah and then of course I mean if if

290
00:13:56,700 --> 00:13:59,160
T is not equal to one so this

291
00:13:59,160 --> 00:14:02,820
I think it feels kind of natural if it's

292
00:14:02,820 --> 00:14:05,100
just a generic T and we map a scaled

293
00:14:05,100 --> 00:14:09,060
version of the of that tangent Vector

294
00:14:09,060 --> 00:14:11,639
PSI we just get gamma T okay

295
00:14:11,639 --> 00:14:14,760
and then there is it's possible also to

296
00:14:14,760 --> 00:14:16,440
define the inverse mapping uh of the

297
00:14:16,440 --> 00:14:19,740
exponential that is called the logarithm

298
00:14:19,740 --> 00:14:22,560
mapping and okay so we have already seen

299
00:14:22,560 --> 00:14:25,620
that in some cases we have a explicit

300
00:14:25,620 --> 00:14:27,839
expressions for both these two mappings

301
00:14:27,839 --> 00:14:31,560
or like in the case of the sphere which

302
00:14:31,560 --> 00:14:33,720
is depicted here we have uh explicit

303
00:14:33,720 --> 00:14:36,240
expression both for for the exponential

304
00:14:36,240 --> 00:14:38,820
mapping and for the remaining logarithm

305
00:14:38,820 --> 00:14:40,740
uh which is not always the case as we

306
00:14:40,740 --> 00:14:43,160
have seen last time for this default

307
00:14:43,160 --> 00:14:43,160
manifold

308
00:14:43,620 --> 00:14:48,600
so here we also have a slide on on this

309
00:14:48,600 --> 00:14:50,579
default manifold the gains as I said so

310
00:14:50,579 --> 00:14:53,639
we have the explicit expression with the

311
00:14:53,639 --> 00:14:56,540
canonical metric it has this form

312
00:14:56,540 --> 00:15:01,019
so as I said we just

313
00:15:01,019 --> 00:15:02,820
choose a t equal to one and and then

314
00:15:02,820 --> 00:15:04,820
this is the expression of the remaining

315
00:15:04,820 --> 00:15:07,800
exponential okay

316
00:15:07,800 --> 00:15:10,800
uh so there is the so there is the base

317
00:15:10,800 --> 00:15:12,839
point and the tangent Vector involved as

318
00:15:12,839 --> 00:15:16,560
we expect

319
00:15:16,560 --> 00:15:19,860
uh this is the Matrix exponential

320
00:15:19,860 --> 00:15:22,980
and yeah okay so at least we have seen

321
00:15:22,980 --> 00:15:25,079
and uh and this is what we have seen

322
00:15:25,079 --> 00:15:27,240
last time so there is no explicit

323
00:15:27,240 --> 00:15:30,199
expression for the Romanian logarithm on

324
00:15:30,199 --> 00:15:30,199
this default manifold

325
00:15:30,839 --> 00:15:39,139
okay another another uh thing uh is the

326
00:15:39,139 --> 00:15:43,139
distance another another tool another

327
00:15:43,139 --> 00:15:46,560
object uh it is defined so the most

328
00:15:46,560 --> 00:15:47,160
general definition is as a

329
00:15:47,160 --> 00:15:49,560
um

330
00:15:49,560 --> 00:15:52,320
yeah it goes through the length

331
00:15:52,320 --> 00:15:54,540
functional okay so we first Define a

332
00:15:54,540 --> 00:15:56,880
length functional of the of the curve

333
00:15:56,880 --> 00:15:58,740
here it doesn't need to be a geodesic

334
00:15:58,740 --> 00:16:01,079
it's just some gamma is just some

335
00:16:01,079 --> 00:16:03,959
parameterized curved on on a manifold

336
00:16:03,959 --> 00:16:07,740
okay it's just some curve on a manifold

337
00:16:07,740 --> 00:16:09,600
and then we compute uh this integral so

338
00:16:09,600 --> 00:16:12,920
this is just gives you the the length

339
00:16:12,920 --> 00:16:15,899
Okay of the curve uh this is the

340
00:16:15,899 --> 00:16:18,959
riemannion metric as I said sometimes we

341
00:16:18,959 --> 00:16:21,720
use G to denote the Romanian method but

342
00:16:21,720 --> 00:16:24,240
usually we use G but in Matrix algorithm

343
00:16:24,240 --> 00:16:26,399
we use other things so it is the

344
00:16:26,399 --> 00:16:29,160
Romanian metric and this is the the

345
00:16:29,160 --> 00:16:32,399
derivative of the curve with respect to

346
00:16:32,399 --> 00:16:35,279
t Okay so this gives you the length

347
00:16:35,279 --> 00:16:39,839
and then the distance is defined as the

348
00:16:39,839 --> 00:16:42,000
minimum of uh over all the the curve

349
00:16:42,000 --> 00:16:43,800
that connect uh so the distance between

350
00:16:43,800 --> 00:16:46,320
two points is defined as the minimum

351
00:16:46,320 --> 00:16:48,180
over all the curves that connect these

352
00:16:48,180 --> 00:16:50,300
two these two points okay so it will be

353
00:16:50,300 --> 00:16:53,699
it is the

354
00:16:53,699 --> 00:16:57,980
it's really the minimal distance Okay so

355
00:16:57,980 --> 00:16:57,980
yeah like that is the distance

356
00:16:58,259 --> 00:17:02,579
um

357
00:17:02,579 --> 00:17:05,220
yeah and then from the from the

358
00:17:05,220 --> 00:17:07,380
Practical point of view so

359
00:17:07,380 --> 00:17:10,079
this is the general definition from the

360
00:17:10,079 --> 00:17:12,419
Practical point of view what we do on

361
00:17:12,419 --> 00:17:13,679
Matrix manifold is that if we are given

362
00:17:13,679 --> 00:17:17,459
two points

363
00:17:17,459 --> 00:17:21,299
and the PSI such that it maps to the

364
00:17:21,299 --> 00:17:26,900
second Point uh then the distance equals

365
00:17:26,900 --> 00:17:30,540
the length of the tangent Vector uh from

366
00:17:30,540 --> 00:17:33,419
that let's say the tangent Vector which

367
00:17:33,419 --> 00:17:36,600
gives the direction of the of the curve

368
00:17:36,600 --> 00:17:38,520
of the judicial and so we can just

369
00:17:38,520 --> 00:17:40,860
compute the distance between two points

370
00:17:40,860 --> 00:17:43,679
as the norm of the tangent Vector with

371
00:17:43,679 --> 00:17:44,820
some Metric with some inner product that

372
00:17:44,820 --> 00:17:47,820
we choose

373
00:17:47,820 --> 00:17:49,980
and we have seen that this is equivalent

374
00:17:49,980 --> 00:17:51,240
to Computing the length of the Romanian

375
00:17:51,240 --> 00:17:52,320
logarithm

376
00:17:52,320 --> 00:17:55,860
uh

377
00:17:55,860 --> 00:17:58,620
of Y with base point x because yeah

378
00:17:58,620 --> 00:18:01,559
because the Romanian logarithm will give

379
00:18:01,559 --> 00:18:03,600
the tangent Vector PSI okay

380
00:18:03,600 --> 00:18:07,320
but this I mean this is just a recap

381
00:18:07,320 --> 00:18:09,780
it's not what we need in this then in in

382
00:18:09,780 --> 00:18:11,760
this type of problems treated in this

383
00:18:11,760 --> 00:18:15,299
paper

384
00:18:15,299 --> 00:18:18,480
um yeah this is this is let's say the

385
00:18:18,480 --> 00:18:23,400
first framework for optimization

386
00:18:23,400 --> 00:18:27,660
algorithms on on manifolds so

387
00:18:27,660 --> 00:18:30,419
as usual we start from the their uh

388
00:18:30,419 --> 00:18:33,539
euclidean counterparts and then we

389
00:18:33,539 --> 00:18:36,120
generalize to to manifolds okay so

390
00:18:36,120 --> 00:18:38,400
recall for example from the from the

391
00:18:38,400 --> 00:18:41,940
notes that I sent you that line search

392
00:18:41,940 --> 00:18:44,820
methods in RN are based on this update

393
00:18:44,820 --> 00:18:48,600
formula so the the new iterate is given

394
00:18:48,600 --> 00:18:52,260
by the previous iterate plus Alpha K

395
00:18:52,260 --> 00:18:56,880
times PK where Alpha K is the step size

396
00:18:56,880 --> 00:18:59,960
or step length is just a real scanner uh

397
00:18:59,960 --> 00:19:05,340
yeah greater than zero

398
00:19:05,340 --> 00:19:07,919
so positive and PK is uh is a vector in

399
00:19:07,919 --> 00:19:11,240
RN which is called search Direction okay

400
00:19:11,240 --> 00:19:14,580
it really gives the direction of search

401
00:19:14,580 --> 00:19:17,940
uh to to look for a minimum okay usually

402
00:19:17,940 --> 00:19:20,520
we in optimization problems most of the

403
00:19:20,520 --> 00:19:23,340
time we consider minimizing a function I

404
00:19:23,340 --> 00:19:25,860
think but okay I mean so I will I will

405
00:19:25,860 --> 00:19:28,559
uh let's say I will

406
00:19:28,559 --> 00:19:31,320
speak interchangeably of minimizing

407
00:19:31,320 --> 00:19:33,000
error and optimizing optimizing in the

408
00:19:33,000 --> 00:19:33,980
sense of minimizing okay with that

409
00:19:33,980 --> 00:19:36,120
meaning

410
00:19:36,120 --> 00:19:37,679
but of course we can also do Max

411
00:19:37,679 --> 00:19:40,559
maximizing

412
00:19:40,559 --> 00:19:46,260
so on on non-linear manifolds

413
00:19:46,260 --> 00:19:49,860
uh how will this generalize so PK will

414
00:19:49,860 --> 00:19:53,039
be a tangent Vector to M at x k

415
00:19:53,039 --> 00:19:56,700
in the sense given a current iterate XK

416
00:19:56,700 --> 00:19:58,100
we on the on the manifold we expand the

417
00:19:58,100 --> 00:20:01,760
tangent space

418
00:20:01,760 --> 00:20:04,039
of we consider the tangent space at m

419
00:20:04,039 --> 00:20:09,299
at X

420
00:20:09,299 --> 00:20:11,880
to M okay and PK will be a tangent

421
00:20:11,880 --> 00:20:15,720
Vector in this tangent space

422
00:20:15,720 --> 00:20:18,480
and then this gives the the direction of

423
00:20:18,480 --> 00:20:22,860
of the curve and then we search along

424
00:20:22,860 --> 00:20:27,120
this curve in M okay with Tangent Vector

425
00:20:27,120 --> 00:20:31,620
at Alpha equal to zero is P K

426
00:20:31,620 --> 00:20:35,940
so this leads us to the concept of

427
00:20:35,940 --> 00:20:40,440
retraction because I mean let's say one

428
00:20:40,440 --> 00:20:42,660
one uh asks uh one might ask okay which

429
00:20:42,660 --> 00:20:45,419
curve which kind of curve does it does

430
00:20:45,419 --> 00:20:47,460
it always need to be a geodesic curve or

431
00:20:47,460 --> 00:20:49,980
can it be also something else

432
00:20:49,980 --> 00:20:51,840
okay so this leads us to the concept of

433
00:20:51,840 --> 00:20:53,940
retraction

434
00:20:53,940 --> 00:20:54,480
so retractions

435
00:20:54,480 --> 00:20:56,840
um

436
00:20:56,840 --> 00:20:59,880
are are met things

437
00:20:59,880 --> 00:21:02,039
are smooth matte things that allow us to

438
00:21:02,039 --> 00:21:03,059
move in the direction of a tangent

439
00:21:03,059 --> 00:21:05,100
vector

440
00:21:05,100 --> 00:21:06,960
while remaining constrained to the

441
00:21:06,960 --> 00:21:09,960
manifold so it's really like this

442
00:21:09,960 --> 00:21:12,600
picture Okay so there is the there is a

443
00:21:12,600 --> 00:21:14,280
curve that is emanating from X in the

444
00:21:14,280 --> 00:21:17,940
direction of PSI

445
00:21:17,940 --> 00:21:19,860
but yeah we we have to stay on on the on

446
00:21:19,860 --> 00:21:22,140
the manifold so

447
00:21:22,140 --> 00:21:23,480
we have to remain constrained to the

448
00:21:23,480 --> 00:21:26,039
manifold

449
00:21:26,039 --> 00:21:28,320
and then there are some local conditions

450
00:21:28,320 --> 00:21:31,559
that preserves that preserve the

451
00:21:31,559 --> 00:21:33,179
gradients at the point x we will see in

452
00:21:33,179 --> 00:21:35,820
the next slide

453
00:21:35,820 --> 00:21:39,240
so the Romanian exponential mapping that

454
00:21:39,240 --> 00:21:42,299
we have seen before in this talk and

455
00:21:42,299 --> 00:21:45,900
previous talk is also a retraction so

456
00:21:45,900 --> 00:21:47,460
because of course it allows us to remain

457
00:21:47,460 --> 00:21:49,200
constrained to the manifold right

458
00:21:49,200 --> 00:21:50,580
because it maps from the tangent space

459
00:21:50,580 --> 00:21:53,760
to the manifold

460
00:21:53,760 --> 00:21:56,460
but from the point of view of uh

461
00:21:56,460 --> 00:21:58,200
numerical algorithms it's not

462
00:21:58,200 --> 00:22:00,659
computationally efficient in general

463
00:22:00,659 --> 00:22:03,360
especially if you start to have big big

464
00:22:03,360 --> 00:22:04,980
problems okay this is because of the way

465
00:22:04,980 --> 00:22:07,260
the

466
00:22:07,260 --> 00:22:09,480
The Matrix exponential which is usually

467
00:22:09,480 --> 00:22:12,299
involved is implemented maybe in the in

468
00:22:12,299 --> 00:22:15,120
the numerical algorithms and also

469
00:22:15,120 --> 00:22:18,059
another thing is that uh

470
00:22:18,059 --> 00:22:19,620
in the numerical algorithm algorithms in

471
00:22:19,620 --> 00:22:21,299
the end we are looking for an

472
00:22:21,299 --> 00:22:23,280
approximate solution not the exact

473
00:22:23,280 --> 00:22:26,400
solution to the problem in any case well

474
00:22:26,400 --> 00:22:29,960
it it will be very close to the to the

475
00:22:29,960 --> 00:22:33,179
to the exact solution if our tolerance

476
00:22:33,179 --> 00:22:37,380
is really small but

477
00:22:37,380 --> 00:22:40,140
so what what I mean is that okay it

478
00:22:40,140 --> 00:22:42,360
doesn't make sense to use the best

479
00:22:42,360 --> 00:22:44,520
possible thing that we have so in this

480
00:22:44,520 --> 00:22:46,500
case the remaining exponential if all

481
00:22:46,500 --> 00:22:48,000
the rest is already approximate and we

482
00:22:48,000 --> 00:22:50,400
are looking for an approximate solution

483
00:22:50,400 --> 00:22:55,440
right so

484
00:22:55,440 --> 00:22:57,960
it's it's better to to also maybe look

485
00:22:57,960 --> 00:23:00,539
for an approximate version of this that

486
00:23:00,539 --> 00:23:02,700
is more efficient that is easier to

487
00:23:02,700 --> 00:23:05,580
compute and more efficient so that's

488
00:23:05,580 --> 00:23:09,000
what retractions are retractions are in

489
00:23:09,000 --> 00:23:11,580
general uh first order approximations to

490
00:23:11,580 --> 00:23:14,640
the Romanian exponential so really like

491
00:23:14,640 --> 00:23:16,799
doing a tailor expansion a Taylor series

492
00:23:16,799 --> 00:23:18,780
and then you you truncated to the first

493
00:23:18,780 --> 00:23:21,980
order and this might give you a

494
00:23:21,980 --> 00:23:21,980
retraction okay

495
00:23:22,140 --> 00:23:27,120
okay

496
00:23:27,120 --> 00:23:30,360
uh do you have any questions

497
00:23:30,360 --> 00:23:31,799
or we can postpone at the end also as

498
00:23:31,799 --> 00:23:36,179
you want

499
00:23:36,179 --> 00:23:39,780
uh yeah so also these tools have been

500
00:23:39,780 --> 00:23:42,419
around for quite some time and yeah this

501
00:23:42,419 --> 00:23:45,480
this is a good paper which explains how

502
00:23:45,480 --> 00:23:47,820
to construct retractions

503
00:23:47,820 --> 00:23:50,640
in general

504
00:23:50,640 --> 00:23:53,159
okay so some more mathematical details

505
00:23:53,159 --> 00:23:53,940
about retraction so properties that we

506
00:23:53,940 --> 00:23:57,000
ask

507
00:23:57,000 --> 00:23:59,760
a retraction mapping to satisfy is the

508
00:23:59,760 --> 00:24:05,159
fact that so the retraction

509
00:24:05,159 --> 00:24:07,740
X of 0x 0x denotes denotes the zero

510
00:24:07,740 --> 00:24:10,200
element in the tangent space so it is an

511
00:24:10,200 --> 00:24:12,299
element it is so it tangent space is a

512
00:24:12,299 --> 00:24:14,580
vector space it has a linear structure

513
00:24:14,580 --> 00:24:16,860
right and that's why we are interesting

514
00:24:16,860 --> 00:24:18,539
every time to like to go through the

515
00:24:18,539 --> 00:24:21,059
tangent space because in the tangent

516
00:24:21,059 --> 00:24:23,400
space it's flat we know how to do things

517
00:24:23,400 --> 00:24:25,740
because yeah we know how to do things in

518
00:24:25,740 --> 00:24:29,700
euclidean space so

519
00:24:29,700 --> 00:24:33,720
the retraction at X of 0x is equal to X

520
00:24:33,720 --> 00:24:35,640
well so if you think like PSI like zero

521
00:24:35,640 --> 00:24:37,980
and then we map to the retraction well

522
00:24:37,980 --> 00:24:38,580
it's equal to x no this is natural I

523
00:24:38,580 --> 00:24:39,840
mean

524
00:24:39,840 --> 00:24:42,179
I mean we don't want to get another

525
00:24:42,179 --> 00:24:45,539
point on the manifold

526
00:24:45,539 --> 00:24:48,480
and then with the identification of the

527
00:24:48,480 --> 00:24:50,820
tangent space at 0x

528
00:24:50,820 --> 00:24:52,080
to the tangent space at x to the

529
00:24:52,080 --> 00:24:54,840
manifold

530
00:24:54,840 --> 00:24:55,799
uh is identified with the tangent space

531
00:24:55,799 --> 00:24:56,820
itself

532
00:24:56,820 --> 00:24:59,760
so

533
00:24:59,760 --> 00:25:03,120
let me explain a bit this so attention

534
00:25:03,120 --> 00:25:05,340
space at X to m is also manifold it is a

535
00:25:05,340 --> 00:25:07,980
linear manifold if the euclidean space

536
00:25:07,980 --> 00:25:09,840
is is a manifold it's flat it just

537
00:25:09,840 --> 00:25:11,880
doesn't have any curvature so we can

538
00:25:11,880 --> 00:25:15,900
still consider a tangent space to that

539
00:25:15,900 --> 00:25:20,520
manifold okay and but this is identified

540
00:25:20,520 --> 00:25:23,360
as the the tangent space itself to M at

541
00:25:23,360 --> 00:25:27,659
X Okay so

542
00:25:27,659 --> 00:25:30,299
uh so this this this with this

543
00:25:30,299 --> 00:25:32,820
identification RX the retraction

544
00:25:32,820 --> 00:25:36,440
satisfies this condition with this which

545
00:25:36,440 --> 00:25:39,240
is called local rigidity condition

546
00:25:39,240 --> 00:25:41,880
such that the differential of the

547
00:25:41,880 --> 00:25:44,000
retraction at the zero element of the

548
00:25:44,000 --> 00:25:45,620
tangent space is equal to the identity

549
00:25:45,620 --> 00:25:48,539
identity

550
00:25:48,539 --> 00:25:51,539
mapping on the tangent spaces so why do

551
00:25:51,539 --> 00:25:54,299
we ask for this for this property the

552
00:25:54,299 --> 00:25:58,080
fact is that uh in the optimization

553
00:25:58,080 --> 00:26:01,200
algorithm we will have also involved the

554
00:26:01,200 --> 00:26:04,740
derivative of the retraction so well we

555
00:26:04,740 --> 00:26:07,559
will yeah the reason is that

556
00:26:07,559 --> 00:26:09,720
okay so maybe let me just explain two

557
00:26:09,720 --> 00:26:11,460
main purposes and I go back to this and

558
00:26:11,460 --> 00:26:13,260
I think it will be clear

559
00:26:13,260 --> 00:26:16,080
so the two main purposes of the

560
00:26:16,080 --> 00:26:17,940
retraction is to turn points on the

561
00:26:17,940 --> 00:26:21,419
tangent space to points on the manifold

562
00:26:21,419 --> 00:26:24,240
okay this just uh just said

563
00:26:24,240 --> 00:26:27,299
because we always want to to be

564
00:26:27,299 --> 00:26:30,000
constrained to the manifold and the

565
00:26:30,000 --> 00:26:31,860
other thing so really in the

566
00:26:31,860 --> 00:26:34,620
optimization algorithm we want to

567
00:26:34,620 --> 00:26:37,140
transform cost functions objective

568
00:26:37,140 --> 00:26:40,340
functions that are defined on the

569
00:26:40,340 --> 00:26:44,039
manifold in a neighborhood of X

570
00:26:44,039 --> 00:26:46,559
usually so locally okay the neighborhood

571
00:26:46,559 --> 00:26:49,200
of X point on the manifold into cost

572
00:26:49,200 --> 00:26:50,400
functions that are defined on the

573
00:26:50,400 --> 00:26:52,860
tangent space

574
00:26:52,860 --> 00:26:54,840
it will also be in a neighborhood okay

575
00:26:54,840 --> 00:26:58,440
it will not be maybe on the whole

576
00:26:58,440 --> 00:27:00,960
tangent space but okay so the cost

577
00:27:00,960 --> 00:27:03,720
function which is sometimes called the

578
00:27:03,720 --> 00:27:06,480
lifted lifted cost function because it

579
00:27:06,480 --> 00:27:08,820
is yeah because it is lifted up

580
00:27:08,820 --> 00:27:12,360
to the tangent space from from the

581
00:27:12,360 --> 00:27:14,760
manifold to the tangent space is a is a

582
00:27:14,760 --> 00:27:17,580
composition of the original cost

583
00:27:17,580 --> 00:27:19,620
function with the retraction okay so the

584
00:27:19,620 --> 00:27:21,720
retraction goes from

585
00:27:21,720 --> 00:27:24,480
so the retraction goes from the tangent

586
00:27:24,480 --> 00:27:26,640
space to the manifold and this function

587
00:27:26,640 --> 00:27:29,580
goes to the manifold from the manifold

588
00:27:29,580 --> 00:27:32,400
to R so the composite function will go

589
00:27:32,400 --> 00:27:36,600
from the tangent space to R

590
00:27:36,600 --> 00:27:39,299
okay so now this this function is

591
00:27:39,299 --> 00:27:41,400
it's really nice for us because it goes

592
00:27:41,400 --> 00:27:43,020
from a euclidean space which is the

593
00:27:43,020 --> 00:27:45,419
tangent space to R

594
00:27:45,419 --> 00:27:47,580
okay so this brings us back let's say to

595
00:27:47,580 --> 00:27:50,279
the euclidean setting in a certain sense

596
00:27:50,279 --> 00:27:51,120
okay at least locally in the tangent

597
00:27:51,120 --> 00:27:52,620
space

598
00:27:52,620 --> 00:27:55,440
okay so that's

599
00:27:55,440 --> 00:27:58,080
this is that's the reason why we ask for

600
00:27:58,080 --> 00:28:02,220
this local rigidity condition okay

601
00:28:02,220 --> 00:28:06,260
because it means that at the at X okay

602
00:28:06,260 --> 00:28:10,500
so zero element uh on that tangent space

603
00:28:10,500 --> 00:28:13,140
we will have a kind of uh yeah we have a

604
00:28:13,140 --> 00:28:15,779
kind of coherence given by the identity

605
00:28:15,779 --> 00:28:18,779
Matrix okay at that point when when we

606
00:28:18,779 --> 00:28:21,140
take differentials of this composite

607
00:28:21,140 --> 00:28:21,140
function

608
00:28:22,860 --> 00:28:28,799
yeah there will be some formulas

609
00:28:28,799 --> 00:28:31,080
afterwards in the context of bfgs about

610
00:28:31,080 --> 00:28:33,440
the differential of of this composite

611
00:28:33,440 --> 00:28:33,440
function

612
00:28:34,860 --> 00:28:41,940
yeah so specifically for embedded sub

613
00:28:41,940 --> 00:28:45,659
manifolds uh their retractions uh so

614
00:28:45,659 --> 00:28:47,419
okay maybe uh yes let me just say so

615
00:28:47,419 --> 00:28:50,100
there is a general recipe

616
00:28:50,100 --> 00:28:52,740
for constructing a retraction on

617
00:28:52,740 --> 00:28:55,860
embedded some manifold and this is due

618
00:28:55,860 --> 00:28:57,659
to the fact that yeah the many the

619
00:28:57,659 --> 00:29:00,720
manifold is embedded in euclidean space

620
00:29:00,720 --> 00:29:02,700
so when we are on the tangent space an

621
00:29:02,700 --> 00:29:04,860
element of the tangent space can be seen

622
00:29:04,860 --> 00:29:05,940
also as an element of the embedding

623
00:29:05,940 --> 00:29:08,520
space

624
00:29:08,520 --> 00:29:10,860
so it's yeah so with the sum let's say a

625
00:29:10,860 --> 00:29:13,980
little abuse of notation we can write

626
00:29:13,980 --> 00:29:16,679
that if we sum X the point of the

627
00:29:16,679 --> 00:29:18,840
manifold plus a tangent Vector we are in

628
00:29:18,840 --> 00:29:22,020
the euclidean space the embedding space

629
00:29:22,020 --> 00:29:24,360
uh calligraphic e

630
00:29:24,360 --> 00:29:28,919
so the recipe is like this so we move

631
00:29:28,919 --> 00:29:30,360
alongside to get to X Plus x i in the

632
00:29:30,360 --> 00:29:34,919
embedding space

633
00:29:34,919 --> 00:29:36,720
and then we map this element X Plus PSI

634
00:29:36,720 --> 00:29:39,720
back to the manifold

635
00:29:39,720 --> 00:29:41,760
for Matrix manifolds we use Matrix

636
00:29:41,760 --> 00:29:44,220
decompositions

637
00:29:44,220 --> 00:29:47,279
so for the sphere for example this is

638
00:29:47,279 --> 00:29:49,140
really easy for the unit hypersphere

639
00:29:49,140 --> 00:29:53,360
because

640
00:29:53,360 --> 00:29:57,419
we just apply this recipe so we move

641
00:29:57,419 --> 00:30:00,000
alongside to get to X Plus PSI and then

642
00:30:00,000 --> 00:30:02,700
to well for this video just a

643
00:30:02,700 --> 00:30:05,460
normalization right so we can we divide

644
00:30:05,460 --> 00:30:08,700
by the norm of this vector and this will

645
00:30:08,700 --> 00:30:11,460
give an element of the unit sphere

646
00:30:11,460 --> 00:30:14,520
because it will be a vector with unitary

647
00:30:14,520 --> 00:30:15,960
Norm so this is really easy for the for

648
00:30:15,960 --> 00:30:18,659
the unit sphere

649
00:30:18,659 --> 00:30:20,720
for the stiffel manifold

650
00:30:20,720 --> 00:30:23,340
as I said usually

651
00:30:23,340 --> 00:30:25,020
retractions are based on on Matrix

652
00:30:25,020 --> 00:30:28,740
decompositions

653
00:30:28,740 --> 00:30:30,960
so given a generic Matrix uh in the

654
00:30:30,960 --> 00:30:34,500
embedding space so in that calligraphic

655
00:30:34,500 --> 00:30:38,399
e in this case a matrix n times P of

656
00:30:38,399 --> 00:30:42,659
size n times p and a full rank this is

657
00:30:42,659 --> 00:30:45,120
the notation of uh for this is notation

658
00:30:45,120 --> 00:30:48,419
often used in numerical analysis for a

659
00:30:48,419 --> 00:30:53,100
full rank matrices

660
00:30:53,100 --> 00:30:55,220
um yeah we have two main decompose

661
00:30:55,220 --> 00:30:58,679
metrics the compositions that are used

662
00:30:58,679 --> 00:31:01,440
uh the polar decomposition which is

663
00:31:01,440 --> 00:31:03,779
which reminds it has this name because

664
00:31:03,779 --> 00:31:06,059
it reminds us of the Polar form of the

665
00:31:06,059 --> 00:31:08,880
complex number so with the with the

666
00:31:08,880 --> 00:31:11,279
radius and the so row e at the power of

667
00:31:11,279 --> 00:31:14,940
I Theta with the radius and Theta the

668
00:31:14,940 --> 00:31:19,919
angle okay so the generic Matrix a can

669
00:31:19,919 --> 00:31:22,140
be the composed by U times P where U is

670
00:31:22,140 --> 00:31:24,960
an element of the stiffel manifold so in

671
00:31:24,960 --> 00:31:27,840
a certain sense is the the part that is

672
00:31:27,840 --> 00:31:31,500
uh real yeah the rotation let's say so

673
00:31:31,500 --> 00:31:36,440
like the Theta in the in the polar form

674
00:31:36,440 --> 00:31:39,240
of the complex number and p uh it's like

675
00:31:39,240 --> 00:31:41,039
analogous of the radius the the role in

676
00:31:41,039 --> 00:31:44,520
the polar form of the complex number so

677
00:31:44,520 --> 00:31:48,059
it is a it is a positive definite

678
00:31:48,059 --> 00:31:48,659
symmetric Matrix okay

679
00:31:48,659 --> 00:31:51,059
um

680
00:31:51,059 --> 00:31:53,399
and the QR factorization which is

681
00:31:53,399 --> 00:31:55,860
basically the gramsmith algorithm for

682
00:31:55,860 --> 00:32:00,720
orthonormalization

683
00:32:00,720 --> 00:32:03,960
uh which gives us the form of Q or for a

684
00:32:03,960 --> 00:32:05,640
q times r with Q again an element of the

685
00:32:05,640 --> 00:32:09,179
stiffer manifold which is what we want

686
00:32:09,179 --> 00:32:10,140
and R is an upper triangular Matrix with

687
00:32:10,140 --> 00:32:13,500
uh

688
00:32:13,500 --> 00:32:16,260
with a positive diagonal elements okay

689
00:32:16,260 --> 00:32:19,740
but these are details and this I mean

690
00:32:19,740 --> 00:32:22,500
details as we look when we Implement

691
00:32:22,500 --> 00:32:25,140
them of course important but I just want

692
00:32:25,140 --> 00:32:26,940
to give you an idea so for this default

693
00:32:26,940 --> 00:32:29,220
manifold the retraction based on the

694
00:32:29,220 --> 00:32:31,860
Polar decomposition looks like this so

695
00:32:31,860 --> 00:32:36,480
there is a there is a part which is X

696
00:32:36,480 --> 00:32:39,000
Plus X I uh so again like from the point

697
00:32:39,000 --> 00:32:41,820
x we move in the direction of the

698
00:32:41,820 --> 00:32:44,399
tangent Vector so we get X Plus x i yeah

699
00:32:44,399 --> 00:32:48,480
like in the sphere exactly the same and

700
00:32:48,480 --> 00:32:51,059
this part this Matrix which is the which

701
00:32:51,059 --> 00:32:53,520
is the inverse of the square root of the

702
00:32:53,520 --> 00:32:56,640
Matrix a square root of this part it's

703
00:32:56,640 --> 00:32:58,500
like the normalization factor if you

704
00:32:58,500 --> 00:33:00,779
want right because it really looks like

705
00:33:00,779 --> 00:33:03,360
the expression for the sphere right so

706
00:33:03,360 --> 00:33:05,279
we have X plus I and then a

707
00:33:05,279 --> 00:33:08,100
normalization factor

708
00:33:08,100 --> 00:33:10,860
so here is this color

709
00:33:10,860 --> 00:33:13,080
here of course is a matrix but I mean

710
00:33:13,080 --> 00:33:15,240
the meaning is is there is an analogy

711
00:33:15,240 --> 00:33:17,100
okay so meaning is the same

712
00:33:17,100 --> 00:33:19,019
and um

713
00:33:19,019 --> 00:33:21,779
yeah the other one so the retraction

714
00:33:21,779 --> 00:33:26,460
based on the QR factorization similarly

715
00:33:26,460 --> 00:33:29,220
we do X Plus PSI and then we do the QR

716
00:33:29,220 --> 00:33:33,360
factorization but in the end we are

717
00:33:33,360 --> 00:33:35,519
interested into only the Q factor so the

718
00:33:35,519 --> 00:33:40,140
part that is on the stiffer manifold so

719
00:33:40,140 --> 00:33:43,320
we let's say we we neglect the the r and

720
00:33:43,320 --> 00:33:48,539
we keep the Q so this is this notation Q

721
00:33:48,539 --> 00:33:52,019
F this stands for Q factor Q F and uh

722
00:33:52,019 --> 00:33:54,480
yeah so okay so we don't care about the

723
00:33:54,480 --> 00:33:56,580
we don't care about the r factor we just

724
00:33:56,580 --> 00:33:59,279
keep the Q

725
00:33:59,279 --> 00:34:02,640
okay so now that we have these

726
00:34:02,640 --> 00:34:08,040
retractions we can build a line search

727
00:34:08,040 --> 00:34:11,520
method on on a manifold so in general uh

728
00:34:11,520 --> 00:34:13,740
uh they use this update formula so where

729
00:34:13,740 --> 00:34:18,179
the new iterate is given by the

730
00:34:18,179 --> 00:34:21,899
retraction at x k of alpha kpk okay so

731
00:34:21,899 --> 00:34:24,839
the the retraction Maps this Alpha kpk

732
00:34:24,839 --> 00:34:26,179
which is a tangent vector scaled by

733
00:34:26,179 --> 00:34:28,619
Alpha K

734
00:34:28,619 --> 00:34:31,980
back to the manifold

735
00:34:31,980 --> 00:34:34,379
and so every every iteration update

736
00:34:34,379 --> 00:34:36,839
looks like this

737
00:34:36,839 --> 00:34:38,520
so the method let's say from a high

738
00:34:38,520 --> 00:34:40,500
level it will look like this so we

739
00:34:40,500 --> 00:34:46,080
choose a retract the line search method

740
00:34:46,080 --> 00:34:48,839
so we choose a retraction R uh well rxk

741
00:34:48,839 --> 00:34:51,060
but usually it stays the same for the

742
00:34:51,060 --> 00:34:53,460
whole algorithm but it might also change

743
00:34:53,460 --> 00:34:55,679
at every iteration actually so we choose

744
00:34:55,679 --> 00:34:58,680
a retraction we select a search

745
00:34:58,680 --> 00:35:01,200
Direction PK in some way

746
00:35:01,200 --> 00:35:03,540
we are going to see this and we select a

747
00:35:03,540 --> 00:35:06,000
step length Alpha K this we are not

748
00:35:06,000 --> 00:35:09,380
going to see the this because

749
00:35:09,380 --> 00:35:12,599
it we would need like a talk only on

750
00:35:12,599 --> 00:35:16,020
line search strategies so

751
00:35:16,020 --> 00:35:19,099
uh yeah we select the step length with

752
00:35:19,099 --> 00:35:22,619
some conditions like our Metro condition

753
00:35:22,619 --> 00:35:25,440
wall for condition causing conditions so

754
00:35:25,440 --> 00:35:28,079
there are many things uh yeah so let's

755
00:35:28,079 --> 00:35:30,480
say we have a way to select the the step

756
00:35:30,480 --> 00:35:32,960
length okay

757
00:35:32,960 --> 00:35:36,180
yeah and regarding the search Direction

758
00:35:36,180 --> 00:35:38,760
so if we choose the search direction as

759
00:35:38,760 --> 00:35:42,300
the anti-gradient so the negative

760
00:35:42,300 --> 00:35:44,700
Romanian gradient of F at x k we get the

761
00:35:44,700 --> 00:35:46,920
Romanian steepest descent so the

762
00:35:46,920 --> 00:35:49,680
Romanian counterpart of steepest descent

763
00:35:49,680 --> 00:35:51,660
method or gradient method some people

764
00:35:51,660 --> 00:35:53,820
call it gradient method some people call

765
00:35:53,820 --> 00:35:56,040
it steepest descent personally I prefer

766
00:35:56,040 --> 00:35:58,619
steepest descent because it gives

767
00:35:58,619 --> 00:36:01,740
already two informations about how this

768
00:36:01,740 --> 00:36:03,119
method is descent because it considers a

769
00:36:03,119 --> 00:36:06,180
descent Direction

770
00:36:06,180 --> 00:36:08,160
a direction in which the function is

771
00:36:08,160 --> 00:36:10,859
decreasing the objective function

772
00:36:10,859 --> 00:36:13,680
decreases and steepest because it

773
00:36:13,680 --> 00:36:17,520
considered the direction for which this

774
00:36:17,520 --> 00:36:20,099
decrease is the is the optimal one the

775
00:36:20,099 --> 00:36:24,000
maximal one yeah instead if you say

776
00:36:24,000 --> 00:36:26,579
gradient method I mean yeah it might be

777
00:36:26,579 --> 00:36:28,320
okay what does that mean there is also

778
00:36:28,320 --> 00:36:30,660
there are also some other methods that

779
00:36:30,660 --> 00:36:33,240
are called gradient method which are not

780
00:36:33,240 --> 00:36:35,820
in optimization so I prefer this term

781
00:36:35,820 --> 00:36:39,859
but in the paper I think they call it

782
00:36:39,859 --> 00:36:39,859
gradient methods if I remember well

783
00:36:40,020 --> 00:36:44,339
yeah and this is the formal algorithm

784
00:36:44,339 --> 00:36:47,280
that is also in the paper of ring and

785
00:36:47,280 --> 00:36:50,520
width it is stated like this so okay

786
00:36:50,520 --> 00:36:52,980
this is just a repetition but I mean the

787
00:36:52,980 --> 00:36:55,320
the pseudo code of the algorithm it

788
00:36:55,320 --> 00:36:58,380
looks like this so you see that

789
00:36:58,380 --> 00:37:01,920
so yeah so you see where the this update

790
00:37:01,920 --> 00:37:04,500
formula enters in and uh yeah so

791
00:37:04,500 --> 00:37:07,859
and as I said if we choose other

792
00:37:07,859 --> 00:37:09,839
directions we get other methods so if we

793
00:37:09,839 --> 00:37:11,400
choose the anti-gradient we get the

794
00:37:11,400 --> 00:37:13,800
steepest descent method if we choose

795
00:37:13,800 --> 00:37:17,280
other directions like we will see later

796
00:37:17,280 --> 00:37:19,560
we can get for example a Newton method

797
00:37:19,560 --> 00:37:21,720
or a quasi-newton method but the

798
00:37:21,720 --> 00:37:24,119
framework is always this algorithm for

799
00:37:24,119 --> 00:37:27,980
this kind of for this kind of methods

800
00:37:27,980 --> 00:37:27,980
for these line search methods

801
00:37:31,920 --> 00:37:40,380
okay and another tool that is important

802
00:37:40,380 --> 00:37:43,400
for a vfgs in particular for this talk

803
00:37:43,400 --> 00:37:47,460
is the parallel transport

804
00:37:47,460 --> 00:37:50,460
we have seen this I mean yeah when we

805
00:37:50,460 --> 00:37:52,140
were when I gave a presentation even if

806
00:37:52,140 --> 00:37:55,140
it was like two or three months ago

807
00:37:55,140 --> 00:37:58,500
about the Federated learning uh on

808
00:37:58,500 --> 00:38:00,300
Romanian manifolds so let me just also

809
00:38:00,300 --> 00:38:01,260
recall a bit

810
00:38:01,260 --> 00:38:04,380
um

811
00:38:04,380 --> 00:38:06,900
so given a Romanian manifold and two

812
00:38:06,900 --> 00:38:09,480
points on it the parallel transport is a

813
00:38:09,480 --> 00:38:12,180
linear operator that preserves the inner

814
00:38:12,180 --> 00:38:13,800
product okay let's state it like this

815
00:38:13,800 --> 00:38:16,500
then I will give some more details so

816
00:38:16,500 --> 00:38:19,020
for any for here you have the specific

817
00:38:19,020 --> 00:38:21,660
case of the sphere so you can fix your

818
00:38:21,660 --> 00:38:24,180
ideas on this illustration so you have

819
00:38:24,180 --> 00:38:26,640
two points X and Y you have the tangent

820
00:38:26,640 --> 00:38:29,820
space to to the mat to the sphere

821
00:38:29,820 --> 00:38:32,099
and both the points okay and you have

822
00:38:32,099 --> 00:38:35,420
let's say our starting point is X and

823
00:38:35,420 --> 00:38:38,460
you have two tangent Vector here PSI and

824
00:38:38,460 --> 00:38:39,119
Zeta Zeta

825
00:38:39,119 --> 00:38:42,240
um

826
00:38:42,240 --> 00:38:44,640
so the parallel transport allows us to

827
00:38:44,640 --> 00:38:48,660
transport these two vectors

828
00:38:48,660 --> 00:38:49,880
in a parallel way let's say this by the

829
00:38:49,880 --> 00:38:52,740
name

830
00:38:52,740 --> 00:38:56,220
to transport them to the tangent space

831
00:38:56,220 --> 00:38:59,339
to m to the sphere at y

832
00:38:59,339 --> 00:39:01,619
in such a way that the parallel

833
00:39:01,619 --> 00:39:05,579
transported PSI and the parallel

834
00:39:05,579 --> 00:39:08,460
transported Zeta when we compute their

835
00:39:08,460 --> 00:39:11,940
inner product gives the same inner

836
00:39:11,940 --> 00:39:14,520
product the same result that we had at X

837
00:39:14,520 --> 00:39:16,740
this is a very desirable property

838
00:39:16,740 --> 00:39:21,599
uh

839
00:39:21,599 --> 00:39:25,320
and the the question so which is also

840
00:39:25,320 --> 00:39:27,720
like a problem question uh

841
00:39:27,720 --> 00:39:30,420
in general so how do we so how do we

842
00:39:30,420 --> 00:39:32,839
transport this because we need the curve

843
00:39:32,839 --> 00:39:36,060
we need the curve to transport

844
00:39:36,060 --> 00:39:38,160
uh these these vectors these tangent

845
00:39:38,160 --> 00:39:40,339
vectors to the other point

846
00:39:40,339 --> 00:39:43,800
so

847
00:39:43,800 --> 00:39:46,920
uh yeah so in general computing the

848
00:39:46,920 --> 00:39:49,740
parallel transport require numerically

849
00:39:49,740 --> 00:39:53,040
solving Odes why because

850
00:39:53,040 --> 00:39:57,000
if you because it is defined on for

851
00:39:57,000 --> 00:39:59,820
geot6 okay for gamma geodesics uh we

852
00:39:59,820 --> 00:40:02,820
have I didn't recall here but last time

853
00:40:02,820 --> 00:40:05,280
we saw that uh like for example for the

854
00:40:05,280 --> 00:40:07,920
stiffel manifold we have a differential

855
00:40:07,920 --> 00:40:09,599
equation to solve for the geodesic then

856
00:40:09,599 --> 00:40:11,579
in the specific case of the stiffel

857
00:40:11,579 --> 00:40:13,680
manifold we have already the explicit

858
00:40:13,680 --> 00:40:16,920
solution but okay let's say in general

859
00:40:16,920 --> 00:40:19,800
we have node to solve to find the

860
00:40:19,800 --> 00:40:23,880
geodesic yeah this is because the

861
00:40:23,880 --> 00:40:26,300
definition of the duty is equals so in

862
00:40:26,300 --> 00:40:28,140
general we should solve node

863
00:40:28,140 --> 00:40:33,119
numerically

864
00:40:33,119 --> 00:40:37,500
and yeah so if if you choose minimizing

865
00:40:37,500 --> 00:40:41,520
geodesic as as a curve to move from X to

866
00:40:41,520 --> 00:40:44,700
Y uh we need to to be able to solve to

867
00:40:44,700 --> 00:40:47,760
compute the Romanian logarithm okay

868
00:40:47,760 --> 00:40:50,700
and we have seen that it's not something

869
00:40:50,700 --> 00:40:53,460
easy to compute and we do not really

870
00:40:53,460 --> 00:40:55,560
need really need that because as I said

871
00:40:55,560 --> 00:40:58,859
in the numerical algorithm already

872
00:40:58,859 --> 00:41:00,720
everything is approximate so okay let's

873
00:41:00,720 --> 00:41:02,500
not just use something that is very

874
00:41:02,500 --> 00:41:03,780
expensive in practice

875
00:41:03,780 --> 00:41:05,040
[Music]

876
00:41:05,040 --> 00:41:08,220
so

877
00:41:08,220 --> 00:41:11,400
yeah I'm saying this so that we will

878
00:41:11,400 --> 00:41:12,900
have alternatives to to this okay to the

879
00:41:12,900 --> 00:41:15,540
parallel transport

880
00:41:15,540 --> 00:41:17,940
and what I described here so this kind

881
00:41:17,940 --> 00:41:20,700
of parallel transport which preserves

882
00:41:20,700 --> 00:41:25,140
the inner product so this is true if we

883
00:41:25,140 --> 00:41:27,720
use the levy civita connection uh I

884
00:41:27,720 --> 00:41:29,760
didn't I don't talk about connections

885
00:41:29,760 --> 00:41:32,099
because it will take too much time so

886
00:41:32,099 --> 00:41:35,579
this is let's say the more uh

887
00:41:35,579 --> 00:41:36,839
theoretical and general aspect

888
00:41:36,839 --> 00:41:39,359
um

889
00:41:39,359 --> 00:41:40,820
okay so this is for the visitor

890
00:41:40,820 --> 00:41:43,980
connection

891
00:41:43,980 --> 00:41:45,900
and we are not going to see this but if

892
00:41:45,900 --> 00:41:48,240
we use other type of

893
00:41:48,240 --> 00:41:50,700
connections we get different properties

894
00:41:50,700 --> 00:41:53,460
so we will not have this but okay

895
00:41:53,460 --> 00:41:55,859
uh yeah

896
00:41:55,859 --> 00:41:58,079
uh so what we because what we do in

897
00:41:58,079 --> 00:41:59,640
practice we do not even use this

898
00:41:59,640 --> 00:42:01,760
parallel transport so we use an

899
00:42:01,760 --> 00:42:06,240
approximation to it

900
00:42:06,240 --> 00:42:08,160
nowadays I found so now because this is

901
00:42:08,160 --> 00:42:11,099
also something that developed like

902
00:42:11,099 --> 00:42:14,400
during the last decade so now in the

903
00:42:14,400 --> 00:42:16,800
most recent references these tools are

904
00:42:16,800 --> 00:42:19,020
called Transporters so the author of

905
00:42:19,020 --> 00:42:22,320
this paper they do not use this name but

906
00:42:22,320 --> 00:42:24,599
it's it's exactly what they do they use

907
00:42:24,599 --> 00:42:26,660
an approximate version of parallel

908
00:42:26,660 --> 00:42:31,280
transport

909
00:42:31,280 --> 00:42:34,380
as as a nicolabumal writes in his book

910
00:42:34,380 --> 00:42:36,900
The Transporter is a poor first man

911
00:42:36,900 --> 00:42:38,060
version of parallel transport so we are

912
00:42:38,060 --> 00:42:40,920
really cheap

913
00:42:40,920 --> 00:42:43,020
and we do not want to compute like the

914
00:42:43,020 --> 00:42:48,000
the parallel transport is too expensive

915
00:42:48,000 --> 00:42:50,339
so we use this tool tool again it's a

916
00:42:50,339 --> 00:42:52,500
linear map that allows the transporter

917
00:42:52,500 --> 00:42:54,960
like the parallel transport is a linear

918
00:42:54,960 --> 00:42:56,240
map that allows to transport tangent

919
00:42:56,240 --> 00:42:59,040
vectors

920
00:42:59,040 --> 00:43:02,339
to the manifold at a certain point to

921
00:43:02,339 --> 00:43:04,500
another one so like like before okay but

922
00:43:04,500 --> 00:43:07,859
here we use another notation okay before

923
00:43:07,859 --> 00:43:08,940
it was P here is T

924
00:43:08,940 --> 00:43:11,940
um

925
00:43:11,940 --> 00:43:13,680
so it is useful is in particular for

926
00:43:13,680 --> 00:43:17,000
this paper in defining a Romanian

927
00:43:17,000 --> 00:43:20,160
version of the classical bfgs algorithm

928
00:43:20,160 --> 00:43:23,579
uh yeah then there are some conditions

929
00:43:23,579 --> 00:43:27,060
okay the the differential so

930
00:43:27,060 --> 00:43:28,680
some way to create to to create parallel

931
00:43:28,680 --> 00:43:31,020
transport is to consider the

932
00:43:31,020 --> 00:43:33,060
differentials of our interaction so this

933
00:43:33,060 --> 00:43:33,800
is very well explained in the in the

934
00:43:33,800 --> 00:43:37,980
book

935
00:43:37,980 --> 00:43:38,960
uh yeah okay but let's just say that for

936
00:43:38,960 --> 00:43:41,700
us

937
00:43:41,700 --> 00:43:43,260
that maybe we are more interested in the

938
00:43:43,260 --> 00:43:46,020
implementation

939
00:43:46,020 --> 00:43:48,660
and we are more interested in embedded

940
00:43:48,660 --> 00:43:49,920
some manifolds of euclidean space The

941
00:43:49,920 --> 00:43:52,020
Transporter

942
00:43:52,020 --> 00:43:55,140
at the end of the day

943
00:43:55,140 --> 00:43:58,140
it is just an orthogonal projection on

944
00:43:58,140 --> 00:43:59,940
the on the tangent space okay so it

945
00:43:59,940 --> 00:44:01,740
becomes really sound it boils down to

946
00:44:01,740 --> 00:44:03,900
something very simple because like for

947
00:44:03,900 --> 00:44:05,819
the stiffel manifold I we also have the

948
00:44:05,819 --> 00:44:08,099
explicit expression for the orthogonal

949
00:44:08,099 --> 00:44:11,460
projection okay

950
00:44:11,460 --> 00:44:13,619
yeah and okay restricted to the tangent

951
00:44:13,619 --> 00:44:17,220
space

952
00:44:17,220 --> 00:44:19,619
so of course it will not uh yeah so it's

953
00:44:19,619 --> 00:44:21,119
it's not a parallel transport but it

954
00:44:21,119 --> 00:44:25,500
will do its job

955
00:44:25,500 --> 00:44:29,940
okay so now we have uh yeah we now have

956
00:44:29,940 --> 00:44:32,040
everything to generalize bfgs to the

957
00:44:32,040 --> 00:44:35,220
to the Romanian setting and this is okay

958
00:44:35,220 --> 00:44:39,300
so so far I was using also other

959
00:44:39,300 --> 00:44:43,920
references uh both this paper the this

960
00:44:43,920 --> 00:44:47,160
references I showed other things and now

961
00:44:47,160 --> 00:44:49,280
it's really about the section 3.1 in the

962
00:44:49,280 --> 00:44:49,280
paper

963
00:44:50,460 --> 00:44:55,859
um

964
00:44:55,859 --> 00:44:59,520
so yeah here there are also many many

965
00:44:59,520 --> 00:45:02,220
many details both uh theoret from a

966
00:45:02,220 --> 00:45:03,960
theoretical point of view and from the

967
00:45:03,960 --> 00:45:05,819
numerical implementation point of view

968
00:45:05,819 --> 00:45:09,240
so I just want to give you the

969
00:45:09,240 --> 00:45:12,599
fundamental ideas and Define the really

970
00:45:12,599 --> 00:45:16,560
important idea of quasi-newton methods

971
00:45:16,560 --> 00:45:20,220
so the fgs is a quasi-newton method

972
00:45:20,220 --> 00:45:23,339
the fundamental idea is that instead of

973
00:45:23,339 --> 00:45:26,760
computing so so quasi-newton methods if

974
00:45:26,760 --> 00:45:29,880
you have if you have read my notes they

975
00:45:29,880 --> 00:45:32,240
consider a search Direction in the same

976
00:45:32,240 --> 00:45:34,980
framework of the line search methods

977
00:45:34,980 --> 00:45:36,660
because instead of considering the minus

978
00:45:36,660 --> 00:45:39,660
gradient they consider a search

979
00:45:39,660 --> 00:45:43,380
direction in which there is

980
00:45:43,380 --> 00:45:45,839
um an approximate Hessian involved we

981
00:45:45,839 --> 00:45:48,619
use we still use first order information

982
00:45:48,619 --> 00:45:51,119
in the sense that we only use

983
00:45:51,119 --> 00:45:54,300
gradients of the cost function we do not

984
00:45:54,300 --> 00:45:56,460
really compute exact hessions which is

985
00:45:56,460 --> 00:45:59,280
second order information we use only

986
00:45:59,280 --> 00:46:01,200
first order information but we use first

987
00:46:01,200 --> 00:46:04,859
order information to get some

988
00:46:04,859 --> 00:46:07,319
information about how the gradient

989
00:46:07,319 --> 00:46:09,300
changes and this gives a kind of second

990
00:46:09,300 --> 00:46:12,839
order information in a certain sense

991
00:46:12,839 --> 00:46:16,800
okay so we look at so if you think also

992
00:46:16,800 --> 00:46:19,980
in like a very simple setting in a

993
00:46:19,980 --> 00:46:22,200
in R2 if you look at how the first

994
00:46:22,200 --> 00:46:25,560
derivative changes

995
00:46:25,560 --> 00:46:27,900
well if you look like at the incremental

996
00:46:27,900 --> 00:46:29,880
ratio of the which involves the first

997
00:46:29,880 --> 00:46:31,560
derivative then you get some kind of

998
00:46:31,560 --> 00:46:33,180
information about about the second

999
00:46:33,180 --> 00:46:36,960
derivative

1000
00:46:36,960 --> 00:46:39,540
okay so this this is the idea used in in

1001
00:46:39,540 --> 00:46:42,000
this quasi-newton methods

1002
00:46:42,000 --> 00:46:44,760
so instead of compute and also in

1003
00:46:44,760 --> 00:46:47,579
addition in addition to considering an

1004
00:46:47,579 --> 00:46:50,160
approximate Hessian instead of computing

1005
00:46:50,160 --> 00:46:53,460
the approximate Hessian which is called

1006
00:46:53,460 --> 00:46:57,300
BK from scratch refresh at every

1007
00:46:57,300 --> 00:47:00,119
iteration we use we we update it so we

1008
00:47:00,119 --> 00:47:02,700
update BK by using the newest

1009
00:47:02,700 --> 00:47:04,079
information that we gained during the

1010
00:47:04,079 --> 00:47:06,540
last iteration

1011
00:47:06,540 --> 00:47:07,619
so that's the reason why I sent you some

1012
00:47:07,619 --> 00:47:10,440
notes because

1013
00:47:10,440 --> 00:47:13,400
there are really many things

1014
00:47:13,400 --> 00:47:16,619
so here I wanted to focus just on the

1015
00:47:16,619 --> 00:47:19,980
tools from Romanian geometry that allows

1016
00:47:19,980 --> 00:47:22,020
to journalize the classical bfgs to

1017
00:47:22,020 --> 00:47:25,560
Romanian manifolds

1018
00:47:25,560 --> 00:47:28,319
so the search direction is chosen as the

1019
00:47:28,319 --> 00:47:31,680
solution to this kind of equations so

1020
00:47:31,680 --> 00:47:34,099
here you have a differential the

1021
00:47:34,099 --> 00:47:37,079
notation is the same as before

1022
00:47:37,079 --> 00:47:40,079
PK but it's not the anti-grain okay so

1023
00:47:40,079 --> 00:47:44,660
it's the direction of the bfgs method

1024
00:47:44,660 --> 00:47:48,780
and BK is a symmetric bilinear operator

1025
00:47:48,780 --> 00:47:52,339
Matrix so it would be symmetric and and

1026
00:47:52,339 --> 00:47:55,920
a positive definite okay

1027
00:47:55,920 --> 00:47:58,920
and is updated according to this formula

1028
00:47:58,920 --> 00:48:03,240
so this is the mathematical statement of

1029
00:48:03,240 --> 00:48:06,960
this sentence okay so we do not compute

1030
00:48:06,960 --> 00:48:09,960
B K plus 1 so the approximate action at

1031
00:48:09,960 --> 00:48:12,480
iteration K plus 1 from scratch but we

1032
00:48:12,480 --> 00:48:15,960
use b k the approximate Hessian at

1033
00:48:15,960 --> 00:48:17,700
iteration K and we update it with these

1034
00:48:17,700 --> 00:48:20,700
formulas okay

1035
00:48:20,700 --> 00:48:24,060
now then we are going to see an analog

1036
00:48:24,060 --> 00:48:28,380
an analogy with the euclidean setting uh

1037
00:48:28,380 --> 00:48:31,740
and we have x k s k Sorry SK is equal to

1038
00:48:31,740 --> 00:48:34,380
Alpha kpk which is in our case given by

1039
00:48:34,380 --> 00:48:36,960
the inverse retraction of x k plus one

1040
00:48:36,960 --> 00:48:38,940
so this is related to what we have just

1041
00:48:38,940 --> 00:48:41,520
seen for the general setting of line

1042
00:48:41,520 --> 00:48:45,420
search methods Alpha kpk

1043
00:48:45,420 --> 00:48:48,000
and y k is the difference of the

1044
00:48:48,000 --> 00:48:51,060
differential of the lifted cost function

1045
00:48:51,060 --> 00:48:53,160
so this f r x k is the one that I

1046
00:48:53,160 --> 00:48:55,500
defined before is the composite function

1047
00:48:55,500 --> 00:48:57,240
of the retraction and the objective

1048
00:48:57,240 --> 00:48:59,099
function f

1049
00:48:59,099 --> 00:49:01,380
Okay so

1050
00:49:01,380 --> 00:49:04,020
this is where we are doing the

1051
00:49:04,020 --> 00:49:06,420
difference between first derivatives the

1052
00:49:06,420 --> 00:49:10,500
differentials so this is where we are

1053
00:49:10,500 --> 00:49:13,980
considering uh A first order information

1054
00:49:13,980 --> 00:49:15,359
to get information about how I mean to

1055
00:49:15,359 --> 00:49:17,099
get some kind of second order

1056
00:49:17,099 --> 00:49:20,220
information so here's the difference

1057
00:49:20,220 --> 00:49:21,420
between the differentials

1058
00:49:21,420 --> 00:49:25,020
and then

1059
00:49:25,020 --> 00:49:28,380
okay and then we have the Transporters

1060
00:49:28,380 --> 00:49:31,859
involved because when we go to the next

1061
00:49:31,859 --> 00:49:35,040
iterate we are not at the same point on

1062
00:49:35,040 --> 00:49:38,400
the manifold anymore so we have to also

1063
00:49:38,400 --> 00:49:40,560
to move the tangent vectors and to move

1064
00:49:40,560 --> 00:49:43,260
the tangent vectors we need the

1065
00:49:43,260 --> 00:49:45,420
Transporters okay so that's why they're

1066
00:49:45,420 --> 00:49:47,160
there yeah we also discussed the

1067
00:49:47,160 --> 00:49:48,180
Transporters and they also discussed in

1068
00:49:48,180 --> 00:49:52,079
the paper

1069
00:49:52,079 --> 00:49:54,599
okay so it has a long history let's say

1070
00:49:54,599 --> 00:49:57,780
the this this already this

1071
00:49:57,780 --> 00:49:59,160
generalization of the fgs

1072
00:49:59,160 --> 00:50:03,720
um

1073
00:50:03,720 --> 00:50:05,579
okay so let's let's see a compare a

1074
00:50:05,579 --> 00:50:07,619
quick comparison with

1075
00:50:07,619 --> 00:50:10,680
the euclidean the star the classical

1076
00:50:10,680 --> 00:50:12,240
bfgs that you have in my in the notes

1077
00:50:12,240 --> 00:50:15,119
that I sent you

1078
00:50:15,119 --> 00:50:16,920
and the Romanian one so here you have

1079
00:50:16,920 --> 00:50:19,800
the difference between successive

1080
00:50:19,800 --> 00:50:22,380
iterates and here you don't have a it's

1081
00:50:22,380 --> 00:50:25,680
it's the analog of the difference so it

1082
00:50:25,680 --> 00:50:28,980
is the inverse retraction at x k of x k

1083
00:50:28,980 --> 00:50:31,680
plus 1 it gives you a tangent Vector it

1084
00:50:31,680 --> 00:50:35,640
is lifted to the tangent space

1085
00:50:35,640 --> 00:50:39,000
and the YK it's the difference of

1086
00:50:39,000 --> 00:50:41,160
euclidean gradients in this case and

1087
00:50:41,160 --> 00:50:44,220
here is the difference of the

1088
00:50:44,220 --> 00:50:45,780
differentials of the lifted objective

1089
00:50:45,780 --> 00:50:49,440
function

1090
00:50:49,440 --> 00:50:53,220
this is the so-called second condition

1091
00:50:53,220 --> 00:50:55,680
in the standard algorithm and this is

1092
00:50:55,680 --> 00:50:58,500
the second condition in the Romanian

1093
00:50:58,500 --> 00:51:01,440
version with Transporters and inverse

1094
00:51:01,440 --> 00:51:03,619
transporter involved and this is the

1095
00:51:03,619 --> 00:51:07,079
update formula for the approximate

1096
00:51:07,079 --> 00:51:08,760
Hessian in the euclidean case

1097
00:51:08,760 --> 00:51:12,119
where you have

1098
00:51:12,119 --> 00:51:15,660
this is the the additional these are the

1099
00:51:15,660 --> 00:51:18,900
additional terms with

1100
00:51:18,900 --> 00:51:21,059
which constitutes a Rank 2 update so

1101
00:51:21,059 --> 00:51:24,420
this is while explained in the book of

1102
00:51:24,420 --> 00:51:27,780
no so that I'm right so it's just this

1103
00:51:27,780 --> 00:51:30,599
is just a vector times its transpose so

1104
00:51:30,599 --> 00:51:33,780
it gives a rank one Matrix this is the

1105
00:51:33,780 --> 00:51:36,480
same it's a rank one Matrix so this as a

1106
00:51:36,480 --> 00:51:38,880
whole is a Rank 2 update

1107
00:51:38,880 --> 00:51:40,380
and we get the new one and here is the

1108
00:51:40,380 --> 00:51:43,020
formula that we just saw in the previous

1109
00:51:43,020 --> 00:51:45,359
slide for the Romanian setting

1110
00:51:45,359 --> 00:51:48,900
okay

1111
00:51:48,900 --> 00:51:51,119
and we also have convergence results and

1112
00:51:51,119 --> 00:51:55,559
convergence rates for this algorithm

1113
00:51:55,559 --> 00:51:58,440
again a lot lots of things of course uh

1114
00:51:58,440 --> 00:52:02,160
for for establishing convergence results

1115
00:52:02,160 --> 00:52:04,440
and convergence rates so I just just

1116
00:52:04,440 --> 00:52:06,780
give the results and and the reference

1117
00:52:06,780 --> 00:52:08,940
if you want to look at the details so we

1118
00:52:08,940 --> 00:52:14,160
have com they established convergence of

1119
00:52:14,160 --> 00:52:18,540
the fgs to the optimal value f x star X

1120
00:52:18,540 --> 00:52:21,839
star is the is the optimum optimizer

1121
00:52:21,839 --> 00:52:23,819
so this is proposition 10. uh

1122
00:52:23,819 --> 00:52:28,500
they established convergence of the

1123
00:52:28,500 --> 00:52:31,260
iterates of bfgs to X star

1124
00:52:31,260 --> 00:52:32,880
so you see this it's the iterate it's

1125
00:52:32,880 --> 00:52:34,440
the convergence of the iterates

1126
00:52:34,440 --> 00:52:37,680
themselves

1127
00:52:37,680 --> 00:52:41,220
starting from a initial initial iterate

1128
00:52:41,220 --> 00:52:47,119
fax zero and then we have M and yeah all

1129
00:52:47,119 --> 00:52:50,579
these m capital M lowercase M and mu

1130
00:52:50,579 --> 00:52:53,400
are constant so

1131
00:52:53,400 --> 00:52:55,520
these two are related to the Hessian I

1132
00:52:55,520 --> 00:52:58,920
think and some bound if I remember well

1133
00:52:58,920 --> 00:53:02,040
and uh yeah this is another constant

1134
00:53:02,040 --> 00:53:04,260
positive constant uh strictly between

1135
00:53:04,260 --> 00:53:06,420
zero and one but okay so it's it's it's

1136
00:53:06,420 --> 00:53:09,780
important of course it has to be less

1137
00:53:09,780 --> 00:53:13,559
than strictly less than one otherwise

1138
00:53:13,559 --> 00:53:14,760
otherwise the bound would not make sense

1139
00:53:14,760 --> 00:53:18,780
um

1140
00:53:18,780 --> 00:53:21,420
and the convergence rate of the fgs is a

1141
00:53:21,420 --> 00:53:24,599
very desirable property because in bfgs

1142
00:53:24,599 --> 00:53:27,300
as I said we use only first order

1143
00:53:27,300 --> 00:53:28,740
information so only information about

1144
00:53:28,740 --> 00:53:33,660
the gradient

1145
00:53:33,660 --> 00:53:35,880
but uh the way it's built allows us to

1146
00:53:35,880 --> 00:53:37,920
achieve super linear convergence okay

1147
00:53:37,920 --> 00:53:39,180
this kind of definition of super linear

1148
00:53:39,180 --> 00:53:42,059
convergence

1149
00:53:42,059 --> 00:53:44,099
so it converts more than linearly

1150
00:53:44,099 --> 00:53:47,460
so linear convergence is the typical

1151
00:53:47,460 --> 00:53:51,800
result for the steepest descent method

1152
00:53:51,800 --> 00:53:54,599
in euclidean setting and it also

1153
00:53:54,599 --> 00:53:58,859
translates to the riemannion setting and

1154
00:53:58,859 --> 00:54:03,240
you can find Details here so same book

1155
00:54:03,240 --> 00:54:05,819
as I said Same Book gives you uh gives

1156
00:54:05,819 --> 00:54:09,359
you convergence of steepest descent to a

1157
00:54:09,359 --> 00:54:11,300
local minimizer indeed iterates so some

1158
00:54:11,300 --> 00:54:14,099
formula like this one

1159
00:54:14,099 --> 00:54:16,140
and it says converges at least linearly

1160
00:54:16,140 --> 00:54:18,480
but okay let's see convergence linearly

1161
00:54:18,480 --> 00:54:21,599
it might be more but this we don't know

1162
00:54:21,599 --> 00:54:23,900
instead for the bfgs we have super

1163
00:54:23,900 --> 00:54:27,059
linear convergence so it's much faster

1164
00:54:27,059 --> 00:54:28,500
and okay this is another comparison with

1165
00:54:28,500 --> 00:54:31,619
another method which is the

1166
00:54:31,619 --> 00:54:33,960
generalization of Newton's method

1167
00:54:33,960 --> 00:54:36,359
which converges quadratically

1168
00:54:36,359 --> 00:54:37,319
okay and this is this is also in the

1169
00:54:37,319 --> 00:54:40,859
same paper

1170
00:54:40,859 --> 00:54:44,040
okay even if then we I they do not use

1171
00:54:44,040 --> 00:54:46,140
it in the applications okay uh in the

1172
00:54:46,140 --> 00:54:48,420
time left let's let's have a look

1173
00:54:48,420 --> 00:54:51,839
quickly at the application to image

1174
00:54:51,839 --> 00:54:55,740
segmentation on the stefl manifold

1175
00:54:55,740 --> 00:54:59,480
uh this is the section 4.2

1176
00:54:59,480 --> 00:55:03,119
so so let's see uh how to

1177
00:55:03,119 --> 00:55:06,000
translate this problem setting into the

1178
00:55:06,000 --> 00:55:09,000
Romanian optimization framework so

1179
00:55:09,000 --> 00:55:12,180
it will become an optimization problem

1180
00:55:12,180 --> 00:55:16,260
in the space of smooth closed curves

1181
00:55:16,260 --> 00:55:19,020
so we can represent a curve by two

1182
00:55:19,020 --> 00:55:23,819
functions e and G from the unity

1183
00:55:23,819 --> 00:55:28,500
interval to R in this way okay so we

1184
00:55:28,500 --> 00:55:31,880
have a starting point and then an

1185
00:55:31,880 --> 00:55:35,099
integral from 0 to Theta so Theta is uh

1186
00:55:35,099 --> 00:55:38,819
must be the the yeah I didn't write but

1187
00:55:38,819 --> 00:55:40,200
the parameter in the unit interval

1188
00:55:40,200 --> 00:55:42,960
and then

1189
00:55:42,960 --> 00:55:45,300
so okay this is the formula plus some

1190
00:55:45,300 --> 00:55:49,980
conditions so we have condition that the

1191
00:55:49,980 --> 00:55:54,059
car of the curve to be closed uh so see

1192
00:55:54,059 --> 00:55:56,520
at the at the end or at the end is equal

1193
00:55:56,520 --> 00:55:59,339
to the first one so it's it's a closed

1194
00:55:59,339 --> 00:56:01,680
curve and unitary length

1195
00:56:01,680 --> 00:56:04,260
okay so this is the formula for the

1196
00:56:04,260 --> 00:56:07,619
length of the parameterized curved

1197
00:56:07,619 --> 00:56:11,220
and these two conditions imply uh

1198
00:56:11,220 --> 00:56:14,940
together they imply on E and G

1199
00:56:14,940 --> 00:56:16,319
uh to be orthonormal in L2 on the unit

1200
00:56:16,319 --> 00:56:17,640
interval

1201
00:56:17,640 --> 00:56:21,660
okay

1202
00:56:21,660 --> 00:56:24,720
so here there is some recall about inner

1203
00:56:24,720 --> 00:56:26,960
product in L2 and induced Norm but okay

1204
00:56:26,960 --> 00:56:31,859
so mathematically

1205
00:56:31,859 --> 00:56:35,180
it means that this couple e and G is an

1206
00:56:35,180 --> 00:56:41,040
element of this manifold so we have

1207
00:56:41,040 --> 00:56:45,180
unitary L2 Norm of e and G okay Norm of

1208
00:56:45,180 --> 00:56:48,540
e equal to Norm of G equal to 1 and uh

1209
00:56:48,540 --> 00:56:50,400
they are normal okay and normality

1210
00:56:50,400 --> 00:56:53,180
condition

1211
00:56:53,180 --> 00:56:59,099
there so yeah also so they are

1212
00:56:59,099 --> 00:57:01,859
orthonormal and this this set is a

1213
00:57:01,859 --> 00:57:05,339
exactly a stiffel manifold

1214
00:57:05,339 --> 00:57:07,980
uh of in the paper in the paper they

1215
00:57:07,980 --> 00:57:11,040
reverse the parameters okay this is our

1216
00:57:11,040 --> 00:57:16,319
usual p equal to two

1217
00:57:16,319 --> 00:57:20,280
okay and a stiffer manifold or on L2 on

1218
00:57:20,280 --> 00:57:20,280
the unit interval tool

1219
00:57:20,400 --> 00:57:24,599
okay I mean in the sense if you we

1220
00:57:24,599 --> 00:57:27,960
discretize this

1221
00:57:27,960 --> 00:57:31,319
it will really be uh something that we

1222
00:57:31,319 --> 00:57:32,819
we have already seen okay

1223
00:57:32,819 --> 00:57:35,819
because in the end of course we

1224
00:57:35,819 --> 00:57:39,240
discretize this thing so it it will have

1225
00:57:39,240 --> 00:57:41,940
some it will have some Dimension and

1226
00:57:41,940 --> 00:57:44,460
some finite number okay because we have

1227
00:57:44,460 --> 00:57:46,500
to implement it in the computer

1228
00:57:46,500 --> 00:57:49,220
so it will not be L2 on the unit

1229
00:57:49,220 --> 00:57:53,900
interval it will be some

1230
00:57:53,900 --> 00:57:53,900
sometimes find it discretization

1231
00:57:55,140 --> 00:58:01,200
okay then uh this was for Curves of

1232
00:58:01,200 --> 00:58:02,460
unitary length

1233
00:58:02,460 --> 00:58:04,920
uh

1234
00:58:04,920 --> 00:58:09,000
uh these authors represent a general

1235
00:58:09,000 --> 00:58:11,940
closed curve so not of unitary length in

1236
00:58:11,940 --> 00:58:15,839
general by an element

1237
00:58:15,839 --> 00:58:21,000
by an element with this uh three uh yeah

1238
00:58:21,000 --> 00:58:24,059
three parts c0 row and the couple e g so

1239
00:58:24,059 --> 00:58:26,280
the couple EG is an element of the

1240
00:58:26,280 --> 00:58:30,540
manifold that the stiffel manifold that

1241
00:58:30,540 --> 00:58:33,420
we just saw and then we have c0 is the

1242
00:58:33,420 --> 00:58:35,000
like the centroid so it's a point in R2

1243
00:58:35,000 --> 00:58:39,119
R2

1244
00:58:39,119 --> 00:58:43,380
and rho is related to the length is the

1245
00:58:43,380 --> 00:58:46,380
length of the curve so it's real okay

1246
00:58:46,380 --> 00:58:49,380
um yeah so the formula generalizes to

1247
00:58:49,380 --> 00:58:52,319
this one okay so we have a x of rho and

1248
00:58:52,319 --> 00:58:53,760
and C zero okay

1249
00:58:53,760 --> 00:58:55,619
and then there are some small

1250
00:58:55,619 --> 00:58:59,579
differences between I think the

1251
00:58:59,579 --> 00:59:02,220
formulation of these authors and uh and

1252
00:59:02,220 --> 00:59:05,819
the one used in the paper of ring and

1253
00:59:05,819 --> 00:59:09,859
weird but okay yeah I mean we are not

1254
00:59:09,859 --> 00:59:09,859
going to implement this now so

1255
00:59:10,140 --> 00:59:15,180
there there is also a metric which is

1256
00:59:15,180 --> 00:59:16,380
given Again by Sandra Morty and and

1257
00:59:16,380 --> 00:59:18,960
others

1258
00:59:18,960 --> 00:59:22,619
uh on the tangent space of curve

1259
00:59:22,619 --> 00:59:25,740
variations and okay besides the details

1260
00:59:25,740 --> 00:59:29,640
what we can see is that there are some

1261
00:59:29,640 --> 00:59:33,119
weights in this metric and these are

1262
00:59:33,119 --> 00:59:35,160
some parameters that we can adjust and

1263
00:59:35,160 --> 00:59:38,880
they do some experiments with these

1264
00:59:38,880 --> 00:59:40,980
parameters to yeah we can see that later

1265
00:59:40,980 --> 00:59:42,599
and then okay there is a closed formula

1266
00:59:42,599 --> 00:59:45,420
for the exponential map

1267
00:59:45,420 --> 00:59:49,819
so yeah yeah so again this kind of this

1268
00:59:49,819 --> 00:59:49,819
kind of Romanian geometry tool okay

1269
00:59:51,599 --> 00:59:54,900
okay so we have to Define an objective

1270
00:59:54,900 --> 00:59:56,880
function

1271
00:59:56,880 --> 00:59:59,540
and the objective function

1272
00:59:59,540 --> 01:00:03,660
uh that we want to

1273
01:00:03,660 --> 01:00:07,020
optimize on on that space so this will

1274
01:00:07,020 --> 01:00:09,599
be our so this will be our our space our

1275
01:00:09,599 --> 01:00:13,920
manifold so this is a manifold part so

1276
01:00:13,920 --> 01:00:18,240
there is a manifold part a real real and

1277
01:00:18,240 --> 01:00:22,920
uh the centroid that belongs to R2

1278
01:00:22,920 --> 01:00:25,319
so this is our constraint okay

1279
01:00:25,319 --> 01:00:27,180
and what is what what do we want to

1280
01:00:27,180 --> 01:00:29,579
minimize what do we want to optimize

1281
01:00:29,579 --> 01:00:33,119
minimize this is the objective function

1282
01:00:33,119 --> 01:00:34,079
so we have a given grayscale image U

1283
01:00:34,079 --> 01:00:38,839
h

1284
01:00:38,839 --> 01:00:43,619
uh okay Define on a on a square

1285
01:00:43,619 --> 01:00:46,140
zero one to R uh H so let's say pixels

1286
01:00:46,140 --> 01:00:48,480
and each pixel is a great value

1287
01:00:48,480 --> 01:00:50,059
and we want to minimize this objective

1288
01:00:50,059 --> 01:00:53,220
functional so

1289
01:00:53,220 --> 01:00:55,799
bracket C is notation for the for the

1290
01:00:55,799 --> 01:00:58,920
curve okay then we are going to write it

1291
01:00:58,920 --> 01:01:02,760
in full but for the moment so we have A1

1292
01:01:02,760 --> 01:01:06,660
A2 just some positive scalar and you

1293
01:01:06,660 --> 01:01:09,900
I and you e are given great values and

1294
01:01:09,900 --> 01:01:12,480
int c and x t denote the interior and

1295
01:01:12,480 --> 01:01:14,160
the exterior of the curve so here I

1296
01:01:14,160 --> 01:01:16,819
highlighted in

1297
01:01:16,819 --> 01:01:20,160
so there is no particular reason why I I

1298
01:01:20,160 --> 01:01:22,799
highlighted you in yellow because I

1299
01:01:22,799 --> 01:01:24,559
wanted to I wanted to see where the

1300
01:01:24,559 --> 01:01:27,299
grayscale image

1301
01:01:27,299 --> 01:01:30,359
enters in the functional okay so this is

1302
01:01:30,359 --> 01:01:33,900
our this is our data so this is our data

1303
01:01:33,900 --> 01:01:36,599
we are given a grayscale image okay and

1304
01:01:36,599 --> 01:01:38,819
this U enters in the functional in these

1305
01:01:38,819 --> 01:01:40,799
two positions and the others are

1306
01:01:40,799 --> 01:01:45,839
parameters that we decide

1307
01:01:45,839 --> 01:01:47,400
because A1 A2 and u i and u e it's our

1308
01:01:47,400 --> 01:01:50,180
choice

1309
01:01:50,180 --> 01:01:53,339
so the first two terms in the functional

1310
01:01:53,339 --> 01:01:57,540
indicate that the curve should enclose

1311
01:01:57,540 --> 01:02:02,640
the image region where U is close to u i

1312
01:02:02,640 --> 01:02:05,280
and far from u e and the third term acts

1313
01:02:05,280 --> 01:02:07,140
as a regularizer and measures the curve

1314
01:02:07,140 --> 01:02:10,200
length which it is actually the curve

1315
01:02:10,200 --> 01:02:14,220
length because it's the integral of

1316
01:02:14,220 --> 01:02:16,799
well it's the integral over the curve of

1317
01:02:16,799 --> 01:02:19,440
DS so it's the curve length in yeah in

1318
01:02:19,440 --> 01:02:22,260
the next expression is replaced by the

1319
01:02:22,260 --> 01:02:24,299
result which is exactly X over row okay

1320
01:02:24,299 --> 01:02:27,000
so this expression

1321
01:02:27,000 --> 01:02:29,760
so as I said we interpret the curve c as

1322
01:02:29,760 --> 01:02:33,420
an element of this manifold

1323
01:02:33,420 --> 01:02:35,040
so it's R2 Cartesian R Cartesian stifel

1324
01:02:35,040 --> 01:02:38,839
manifold

1325
01:02:38,839 --> 01:02:41,940
and they add the term for a uniform part

1326
01:02:41,940 --> 01:02:44,040
parametrization of the curve so here now

1327
01:02:44,040 --> 01:02:46,980
instead of the bracket C we really write

1328
01:02:46,980 --> 01:02:49,319
explicitly the the parameter let's say

1329
01:02:49,319 --> 01:02:52,079
the elements of the curve we have the

1330
01:02:52,079 --> 01:02:55,440
centroid the length and the two function

1331
01:02:55,440 --> 01:02:58,260
that parametrize the curve e and G okay

1332
01:02:58,260 --> 01:03:00,119
that couple there and then it's it's the

1333
01:03:00,119 --> 01:03:04,619
same expression as before just we write

1334
01:03:04,619 --> 01:03:08,220
c0 row EMG and we add this term okay so

1335
01:03:08,220 --> 01:03:10,859
this is the final objective functional

1336
01:03:10,859 --> 01:03:15,359
and for the numerical implementation as

1337
01:03:15,359 --> 01:03:17,280
I anticipated Eng are discretized of

1338
01:03:17,280 --> 01:03:19,200
course so they are discretized as

1339
01:03:19,200 --> 01:03:21,839
piecewise constant functions on a

1340
01:03:21,839 --> 01:03:25,200
uniform grid over the unit interval

1341
01:03:25,200 --> 01:03:28,140
and the image U is given yeah we also

1342
01:03:28,140 --> 01:03:31,200
say is a pixel values on a uniform grid

1343
01:03:31,200 --> 01:03:35,040
yeah sorry I ran a bit

1344
01:03:35,040 --> 01:03:37,859
later but let's just see so the last

1345
01:03:37,859 --> 01:03:40,799
example so they they show

1346
01:03:40,799 --> 01:03:46,020
they tried some general numerical

1347
01:03:46,020 --> 01:03:49,319
examples on curve Evolution using bfgs

1348
01:03:49,319 --> 01:03:51,420
and also comparing with other methods so

1349
01:03:51,420 --> 01:03:56,579
here you see they start with the square

1350
01:03:56,579 --> 01:03:59,099
and and they they they find the so they

1351
01:03:59,099 --> 01:04:03,660
want to do segmentation of these of this

1352
01:04:03,660 --> 01:04:05,599
image this white image there and uh yeah

1353
01:04:05,599 --> 01:04:08,339
so you have

1354
01:04:08,339 --> 01:04:11,520
several steps so

1355
01:04:11,520 --> 01:04:15,000
this is Step numbers step number seven

1356
01:04:15,000 --> 01:04:16,920
and this is at convergence okay so there

1357
01:04:16,920 --> 01:04:19,500
are not all the steps so it must be some

1358
01:04:19,500 --> 01:04:21,720
Step at around 40 right

1359
01:04:21,720 --> 01:04:23,880
and this is the convergence of the

1360
01:04:23,880 --> 01:04:26,099
objective functional to the optimal to

1361
01:04:26,099 --> 01:04:28,680
the optimal well to the optimal level to

1362
01:04:28,680 --> 01:04:32,760
the approximate optimal value because

1363
01:04:32,760 --> 01:04:35,160
they stop uh they stop at 10 at the

1364
01:04:35,160 --> 01:04:39,839
minus eight okay

1365
01:04:39,839 --> 01:04:42,599
and this is interesting so it's a

1366
01:04:42,599 --> 01:04:44,520
comparison with other methods okay in

1367
01:04:44,520 --> 01:04:47,700
this talk we have seen just these two

1368
01:04:47,700 --> 01:04:50,339
the steepest descent method and the bfgs

1369
01:04:50,339 --> 01:04:52,859
quasi-newton method and then they also

1370
01:04:52,859 --> 01:04:55,619
consider two types of retraction one

1371
01:04:55,619 --> 01:04:58,260
they they call it geodesic retraction so

1372
01:04:58,260 --> 01:05:01,020
it's just uh it's just a remaining

1373
01:05:01,020 --> 01:05:03,180
exponential because yeah it's the

1374
01:05:03,180 --> 01:05:05,579
retraction with the with the geodesic is

1375
01:05:05,579 --> 01:05:09,059
the Romanian exponential and this is not

1376
01:05:09,059 --> 01:05:09,960
so this is the other let's say cheaper

1377
01:05:09,960 --> 01:05:12,720
version

1378
01:05:12,720 --> 01:05:14,760
but you see that we do not have that

1379
01:05:14,760 --> 01:05:18,059
much difference in the iteration numbers

1380
01:05:18,059 --> 01:05:20,579
between the the cheaper version and the

1381
01:05:20,579 --> 01:05:22,520
more expensive version

1382
01:05:22,520 --> 01:05:25,020
but what is

1383
01:05:25,020 --> 01:05:26,640
more striking is the difference between

1384
01:05:26,640 --> 01:05:29,880
the number of iteration of gradient

1385
01:05:29,880 --> 01:05:32,359
Descent of steepest descent and bfgs so

1386
01:05:32,359 --> 01:05:37,260
you see this is in the order of

1387
01:05:37,260 --> 01:05:40,500
or there are one one thousand so it's

1388
01:05:40,500 --> 01:05:42,839
almost too hard almost to

1389
01:05:42,839 --> 01:05:44,280
orders of magnitude of difference well

1390
01:05:44,280 --> 01:05:47,160
that is one

1391
01:05:47,160 --> 01:05:48,740
so it's really it's really much much

1392
01:05:48,740 --> 01:05:51,839
faster

1393
01:05:51,839 --> 01:05:53,220
bfgs it really shows a super linear

1394
01:05:53,220 --> 01:05:56,460
convergence

1395
01:05:56,460 --> 01:05:59,700
and yeah so

1396
01:05:59,700 --> 01:06:02,579
I mean it's a method they consider here

1397
01:06:02,579 --> 01:06:05,700
it's yeah it's it's nice I think it's

1398
01:06:05,700 --> 01:06:07,680
really cool so the experiments they also

1399
01:06:07,680 --> 01:06:10,200
did some experiments for different

1400
01:06:10,200 --> 01:06:11,420
weights in the inside the metric that we

1401
01:06:11,420 --> 01:06:15,000
just saw

1402
01:06:15,000 --> 01:06:18,180
uh which I report here for reference

1403
01:06:18,180 --> 01:06:22,260
and they see they they show through the

1404
01:06:22,260 --> 01:06:24,960
experiments that a larger Lambda D so

1405
01:06:24,960 --> 01:06:28,440
the first uh the top row here the first

1406
01:06:28,440 --> 01:06:31,380
the first row so from left to right

1407
01:06:31,380 --> 01:06:34,319
so the larger Lambda D ensures a good

1408
01:06:34,319 --> 01:06:37,680
curve positioning and scaling before

1409
01:06:37,680 --> 01:06:41,099
starting the major deformations so as

1410
01:06:41,099 --> 01:06:43,440
you can see this is a square that so

1411
01:06:43,440 --> 01:06:45,839
this I think is yeah they are not

1412
01:06:45,839 --> 01:06:48,180
successive iterations but so this is is

1413
01:06:48,180 --> 01:06:49,740
a square at the beginning and here is

1414
01:06:49,740 --> 01:06:52,099
still a square here is still a square

1415
01:06:52,099 --> 01:06:55,559
and yeah so

1416
01:06:55,559 --> 01:06:58,020
this does the positioning first and the

1417
01:06:58,020 --> 01:07:00,599
skip positioning and scaling first this

1418
01:07:00,599 --> 01:07:03,359
top row

1419
01:07:03,359 --> 01:07:06,599
um as with the with a big Lambda D with

1420
01:07:06,599 --> 01:07:09,420
a small Lambda D is the opposite we

1421
01:07:09,420 --> 01:07:11,700
start the major deformation first so

1422
01:07:11,700 --> 01:07:15,180
from a square it or it is already the

1423
01:07:15,180 --> 01:07:18,359
form that the subsequent iteration so it

1424
01:07:18,359 --> 01:07:19,619
does first the deformations and then the

1425
01:07:19,619 --> 01:07:24,299
positioning

1426
01:07:24,299 --> 01:07:27,980
yeah so this is the this is the the the

1427
01:07:27,980 --> 01:07:31,280
influence the impact of this two

1428
01:07:31,280 --> 01:07:31,280
parameters okay

1429
01:07:32,760 --> 01:07:39,180
and the last experiment is the Contour

1430
01:07:39,180 --> 01:07:40,339
segmentation of the camera cameraman

1431
01:07:40,339 --> 01:07:45,180
image

1432
01:07:45,180 --> 01:07:47,220
uh yeah okay so here maybe I think this

1433
01:07:47,220 --> 01:07:49,559
figure is a bit confusing but I didn't

1434
01:07:49,559 --> 01:07:52,799
have time to present it better maybe

1435
01:07:52,799 --> 01:07:56,339
because so if you read the the caption

1436
01:07:56,339 --> 01:07:59,520
so in the first in the top row

1437
01:07:59,520 --> 01:08:03,720
um in the top row the the last step is

1438
01:08:03,720 --> 01:08:08,359
46 is the 46th iteration but in the

1439
01:08:08,359 --> 01:08:11,780
second row the last the last image is

1440
01:08:11,780 --> 01:08:15,359
116. so let's say what I mean is okay

1441
01:08:15,359 --> 01:08:17,759
among the three rows it's not really a

1442
01:08:17,759 --> 01:08:20,779
fair comparison in terms of the number

1443
01:08:20,779 --> 01:08:26,040
of iteration because here is 46

1444
01:08:26,040 --> 01:08:29,160
uh here is 116 and here is 250. yeah so

1445
01:08:29,160 --> 01:08:32,640
okay we are comparing a bit uh but okay

1446
01:08:32,640 --> 01:08:34,500
uh the and each row also has

1447
01:08:34,500 --> 01:08:38,279
okay that makes sense so it has

1448
01:08:38,279 --> 01:08:41,880
different parameters of A1 A2 and A3

1449
01:08:41,880 --> 01:08:45,839
okay so it's just I think

1450
01:08:45,839 --> 01:08:48,540
I think it's just a visual a visual

1451
01:08:48,540 --> 01:08:50,880
example of how it works is not really

1452
01:08:50,880 --> 01:08:54,600
for the numerics here so it's really

1453
01:08:54,600 --> 01:08:58,259
like a visual outcome so you see that uh

1454
01:08:58,259 --> 01:09:00,120
for example uh here you use many more

1455
01:09:00,120 --> 01:09:03,480
iterations with respect to the previous

1456
01:09:03,480 --> 01:09:05,880
two rows the first and the second but

1457
01:09:05,880 --> 01:09:08,160
you have a good segmentation I mean at

1458
01:09:08,160 --> 01:09:10,560
least visually speaking I mean I think

1459
01:09:10,560 --> 01:09:14,400
that's the reason why they showed this

1460
01:09:14,400 --> 01:09:18,120
and uh yeah it's the and actually the

1461
01:09:18,120 --> 01:09:20,580
the norm the norm of the gradient so the

1462
01:09:20,580 --> 01:09:22,259
discretized object the the derivative of

1463
01:09:22,259 --> 01:09:24,359
the discretized objective function is

1464
01:09:24,359 --> 01:09:27,600
not even that small I mean the tolerance

1465
01:09:27,600 --> 01:09:29,040
of it the stopping Criterion is that 10

1466
01:09:29,040 --> 01:09:32,880
and the minus two

1467
01:09:32,880 --> 01:09:38,100
and these are all experiments with bfgs

1468
01:09:38,100 --> 01:09:39,299
this these three rows and all for Lambda

1469
01:09:39,299 --> 01:09:42,719
l

1470
01:09:42,719 --> 01:09:46,440
equal to Lambda D equal to 1. so they

1471
01:09:46,440 --> 01:09:49,739
chose uh that that thing so the Lambda D

1472
01:09:49,739 --> 01:09:52,500
equal to one so we are in this we are in

1473
01:09:52,500 --> 01:09:54,660
this context okay so we first do the

1474
01:09:54,660 --> 01:09:58,100
major they so in these experiments

1475
01:09:58,100 --> 01:10:01,020
basically they first do the deformations

1476
01:10:01,020 --> 01:10:03,900
and then the the positioning I think

1477
01:10:03,900 --> 01:10:06,060
should be like that

1478
01:10:06,060 --> 01:10:10,980
Okay so

1479
01:10:10,980 --> 01:10:14,100
what are the few Pro and contrast so the

1480
01:10:14,100 --> 01:10:16,620
pros is I think I hope convinced you

1481
01:10:16,620 --> 01:10:19,500
that there is a lot of theory behind it

1482
01:10:19,500 --> 01:10:22,199
so the pro is that

1483
01:10:22,199 --> 01:10:25,199
uh the theory is well understood

1484
01:10:25,199 --> 01:10:27,179
so there is really so we know so we know

1485
01:10:27,179 --> 01:10:31,739
what we are doing in the sense

1486
01:10:31,739 --> 01:10:34,320
uh yeah it's not just uh I don't know uh

1487
01:10:34,320 --> 01:10:37,679
we apply some method and we don't know

1488
01:10:37,679 --> 01:10:39,960
how we don't know why why it works so we

1489
01:10:39,960 --> 01:10:43,920
went we went from

1490
01:10:43,920 --> 01:10:46,500
uh we went from manifolds uh metrics

1491
01:10:46,500 --> 01:10:48,900
manifolds optimization algorithms and

1492
01:10:48,900 --> 01:10:51,840
then we take this problem setting we

1493
01:10:51,840 --> 01:10:52,980
discretize Etc so we we know we know why

1494
01:10:52,980 --> 01:10:56,400
it works

1495
01:10:56,400 --> 01:10:59,880
uh uh the Contra so for that specific

1496
01:10:59,880 --> 01:11:01,940
example the image segmentation is that

1497
01:11:01,940 --> 01:11:05,400
it cannot deal with with

1498
01:11:05,400 --> 01:11:07,679
self-intersecting curves so yeah as soon

1499
01:11:07,679 --> 01:11:09,320
as they they State the authors as soon

1500
01:11:09,320 --> 01:11:12,540
as the Curve

1501
01:11:12,540 --> 01:11:14,699
self-intersect in the in these examples

1502
01:11:14,699 --> 01:11:17,760
they had to stop the algorithm because

1503
01:11:17,760 --> 01:11:19,080
then probably I mean does not converge

1504
01:11:19,080 --> 01:11:20,110
anymore

1505
01:11:20,110 --> 01:11:26,870
okay

1506
01:11:26,870 --> 01:11:26,870
[Music]