# -*- coding: utf-8 -*-
"""Untitled0.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/13b4qFo5nO0ePzl-HiTHBYS4o1H3HU5q8
"""



"""# Color Exploration on Maps

## Downloading the Required Files from Google Drive
"""

import os
import gdown  # download shared Google Drive files and folders

if not os.path.exists('maps.tar.gz'):
    try:  # download 'maps.tar.gz' from Google Drive
        # URL to shared 'maps.tar.gz' archive
        url = 'https://drive.google.com/file/d/1r9X5sXfCuNHd58v36XdgrzvMUD_HM7La/view'
        gdown.download(url, quiet=True, fuzzy=True)
    except:  # download 'maps.tar.gz' from the Internet
        !wget 'http://efrosgans.eecs.berkeley.edu/pix2pix/datasets/maps.tar.gz'

!ls -hal 'maps.tar.gz'  # check file exists

# Extract data from archive
import zipfile
import tarfile

def extract(fname):
    """ Extract files from archive. """
    if fname.endswith(".tar.gz") or fname.endswith('.tgz'):
        ref = tarfile.open(fname, mode='r:gz')
    elif fname.endswith('.tar'):
        ref = tarfile.open(fname, mode='r:')
    elif fname.endswith('.tar.bz2') or fname.endswith('.tbz'):
        ref = tarfile.open(fname, mode='r:bz2')
    elif fname.endswith('.zip'):
        ref = zipfile.ZipFile(fname, mode='r')

    ref.extractall()
    ref.close()

extract('maps.tar.gz')

"""## Importing Libraries"""

import os
import cv2
import numpy as np
# import multiprocessing
import matplotlib.pyplot as plt
# import albumentations as A  # image augmentation library

# import torch
# import torch.nn as nn
# import torch.optim as optim

from PIL import Image
# from torchvision.utils import save_image
# from albumentations.pytorch import ToTensorV2
# from torch.utils.data import Dataset, DataLoader
# from tqdm.notebook import tqdm  # library to show the progressbar

"""## Showing Several Maps"""

def get_images(filepath):
    """ Get images from filepath. """
    image = Image.open(filepath)  # open file via Pillow (PIL) library
    image = np.array(image)  # convert from PIL to NumPy format
    image_photo = image[:, :600, :]  # photo image
    image_map = image[:, 600:, :]  # map image
    return image_photo, image_map


def get_images_from_dir(path, index):
    """ Get images from directory by index. """
    list_files = os.listdir(path)  # get list of all files in directory
    filepath = os.path.join(path, list_files[index])  # get file by index
    return get_images(filepath)


def add_image(image, title, cmap='viridis'):
    """ Add image to the image grid. Default color map is 'viridis'. """
    plt.imshow(image, cmap=cmap)  # add image to the image grid
    plt.title(title)  # show image title
    plt.axis('off')  # turn off axis numbers


def show_images(image_photo, image_map):
    fig = plt.figure(figsize=(8, 4))  # create figure with size 12×4 inches

    fig.add_subplot(1, 2, 1)  # add a new cell to the grid
    add_image(image_photo, 'Photo')

    fig.add_subplot(1, 2, 2)  # add a new cell to the grid
    add_image(image_map, 'Map')

    plt.show()  # show the grid


def show(image, size=(10, 10), title=None):
    """ Show image in Jupyter Notebook. """
    plt.figure(figsize=size)  # set image size in inches
    add_image(image, title)
    plt.show


for i in range(4):
    image_photo, image_map = get_images_from_dir('./maps/val', i)
    show_images(image_photo, image_map)

"""## Exploring Colors on Maps

### Exploring Colors on One Map
"""

def show_hist(image, size=(10, 10), title=None):
    """ Display grayscale histogram from image path
        using Matplotlib method `pyplot.hist`. """
    image = Image.fromarray(image)  # convert from NumPy to PIL format
    image = image.convert('L')  # convert to grayscale
    image = np.array(image)  # convert from PIL to NumPy format
    plt.figure(figsize=size)  # set size in inches
    plt.title(title)
    plt.hist(image.ravel(), bins=range(256), range=(0, 1))
    plt.show


def show_hist2(histogram, title=None):
    """ Plot the global histogram. """
    plt.figure(figsize=(10, 10))
    plt.bar(range(256), histogram, width=1.0, color='gray')
    plt.title(title)
    plt.xlabel('Pixel Intensity')
    plt.ylabel('Frequency')
    plt.grid(axis='y', alpha=0.75)
    plt.show()


index = 0
_, image_map = get_images_from_dir('./maps/val', index)
print(f'Map size: {image_map.shape}')

# Reshape (height, width, channels) into a 2D array where each row represents a
# pixel and its color channels. Reshape from (600, 600, 3) to (360 000, 3) array.
pixels = image_map.reshape(-1, image_map.shape[-1])
print(f'Reshape {image_map.shape} to {pixels.shape}')

unique_colors = np.unique(pixels, axis=0)  # find unique rows (unique colors)
num_unique_colors = len(unique_colors)
print(f'Number of unique colors: {num_unique_colors}')

print(unique_colors.shape)

white_color = (255, 255, 255)
print()
if white_color in unique_colors:
    print('Yes, there is white color.')
else:
    print('No, there is no white color.')

show_hist(image_map)

"""### Exploring Colors on All Maps"""

global_histogram = np.zeros(256)
train_dir = './maps/train'
list_files = os.listdir(train_dir)  # get list of all files in directory
print(f'There are {len(list_files)} maps in {train_dir}\n')

for i in range(len(list_files)):
# for i in range(2):
    if list_files[i].lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
        filepath = os.path.join(train_dir, list_files[i])  # get file by index
        try:
            _, image_map = get_images(filepath)

            # Calculate histogram for the current image
            hist_counts, _ = np.histogram(image_map.flatten(), bins=256, range=(0, 255))

            # Add to the global histogram
            global_histogram += hist_counts
        except Exception as e:
            print(f"Error processing {filepath}: {e}")

show_hist2(global_histogram, f'Combined Histogram of {len(list_files)} Images')

"""## Color Thresholding

The core idea is to isolate specific color ranges within an image by setting lower and upper bounds for the color components.
"""

def get_contours(image,
                 mode=cv2.RETR_EXTERNAL,  # find outermost contours by default
                 method=cv2.CHAIN_APPROX_SIMPLE):
    """ Find contours both for OpenCV v3.x and v4.x. """
    # Find the outermost contours, based on the binary dice image
    output = cv2.findContours(image=image, mode=mode, method=method)
    if len(output) == 3:  # (_, contours, hierarchy)
        return (output[1], output[2])
    elif len(output) == 2:  # (contours, hierarchy)
        return output


def show_contours(image, mask, size, title, inv=False, mode=cv2.RETR_EXTERNAL):
    """ Draw contours on the image and show it. """
    # Create binary image
    _, binary = cv2.threshold(mask, 1, 255, cv2.THRESH_BINARY)

    # Find outermost contours
    contours, _ = get_contours(
        image=binary,
        mode=mode,  # find outermost contours by default
        method=cv2.CHAIN_APPROX_SIMPLE)

    # for i, c in enumerate(contours, 1):
    #     print(f"\tSize of contour {i}: {len(c)}")

    # Draw contours on the image
    cv2.drawContours(image=image, contours=contours, contourIdx=-1,
                     color=(255, 0, 0), thickness=1)
    show(image, size, title)



def get_masks(path, lower, upper, area=100):
    """ Get masks from the maps using color thresholding. """
    list_files = os.listdir(train_dir)  # get list of all files in directory

    for i in range(5):
        if list_files[i].lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
            filepath = os.path.join(train_dir, list_files[i])  # get file by index
            try:
                image = cv2.imread(filepath)
                image_photo = image[:, :600, :]  # photo image
                image_map = image[:, 600:, :]  # map image

                # image_map = cv2.GaussianBlur(image_map, (5, 5), 0)  # blur image

                # Convert to HSV Color Space: While you can work with RGB,
                # converting to HSV (Hue, Saturation, Value) is often preferred for
                # color thresholding because it separates color information (Hue)
                # from brightness (Value) and intensity (Saturation),
                # making it easier to define color ranges.
                hsv_image = cv2.cvtColor(image_map, cv2.COLOR_BGR2HSV)

                # Define Color Range (Lower and Upper Bounds):
                # Determine the HSV values that define the color you want to isolate.
                # These values are typically found through experimentation
                # or by using color picker tools.

                mask = cv2.inRange(hsv_image, lower, upper)  # create mask
                # result = cv2.bitwise_and(image_map, image_map, mask=mask)

                # # Noise reduction. Version 1.
                # # Define a kernel (e.g., a 3x3 or 5x5 square kernel)
                # kernel = np.ones((3, 3), np.uint8)
                # # Perform Opening (erosion followed by dilation) to remove small dots
                # opened_mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                # # Perform Closing (dilation followed by erosion) to fill small holes (optional)
                # closed_mask = cv2.morphologyEx(opened_mask, cv2.MORPH_CLOSE, kernel)


                # Noise reduction. Version 2.
                # Create binary image
                _, binary = cv2.threshold(mask, 1, 255, cv2.THRESH_BINARY)

                mode = cv2.RETR_TREE  # find all contours hierarchy
                # mode=cv2.RETR_EXTERNAL  # find outermost contours
                contours, _ = get_contours(  # find contours
                    image=binary,
                    mode=mode,
                    method=cv2.CHAIN_APPROX_SIMPLE)

                contours_to_remove = []  # remove noise contours
                # Example: Remove contours smaller than 100 pixels in area
                for c in contours:
                    if cv2.contourArea(c) < area:
                        contours_to_remove.append(c)

                removal_mask = np.zeros_like(mask)
                cv2.drawContours(removal_mask, contours_to_remove, -1, 255, cv2.FILLED)

                cleaned_mask = cv2.bitwise_and(mask, cv2.bitwise_not(removal_mask))

                # Show images
                fig = plt.figure(figsize=(15, 5))  # create figure with size 12×4 inches
                fig.add_subplot(1, 3, 1)  # add a new cell to the grid
                add_image(image_photo, 'Photo')
                fig.add_subplot(1, 3, 2)  # add a new cell to the grid
                add_image(image_map, 'Map')
                fig.add_subplot(1, 3, 3)  # add a new cell to the grid
                add_image(cleaned_mask, 'Cleaned Mask')
                plt.show()  # show the grid

                show_contours(image_photo, cleaned_mask, (12, 12), 'Contours', inv=False, mode=mode)
                show(cleaned_mask, size=(12, 12), title='Cleaned mask')

            except Exception as e:
                print(f"Error processing {filepath}: {e}")

# white color (medium sized roads)
hsv_lower = np.array([ 0,  0, 245])  # best value: [ 0,  0, 245]
hsv_upper = np.array([165, 5, 255])  # best value: [165, 5, 255]
threshold_area = 100  # best value: 100

get_masks('./maps/train', hsv_lower, hsv_upper, threshold_area)

# green color on the map
hsv_lower = np.array([34, 52, 219])  # best value: [34, 52, 219]
hsv_upper = np.array([43, 68, 226])  # best value: [43, 68, 226]
threshold_area = 10  # best value: 5-10

get_masks('./maps/train', hsv_lower, hsv_upper, threshold_area)

