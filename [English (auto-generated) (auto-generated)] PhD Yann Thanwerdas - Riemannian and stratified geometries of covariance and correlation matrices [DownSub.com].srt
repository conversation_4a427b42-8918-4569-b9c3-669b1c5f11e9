1
00:00:00,000 --> 00:00:04,480
so now we are on we are on youtube right

2
00:00:04,480 --> 00:00:04,480
okay yep

3
00:00:08,240 --> 00:00:11,280
yep so we are live

4
00:00:12,080 --> 00:00:16,960
it's amazing actually we do it like

5
00:00:16,960 --> 00:00:19,960
phd depends on me on youtube it's

6
00:00:19,960 --> 00:00:19,960
amazing

7
00:00:45,280 --> 00:00:49,200
so are we doing everything online or

8
00:00:49,200 --> 00:00:51,600
other people are like you know attending

9
00:00:51,600 --> 00:00:55,160
there are some members here

10
00:00:55,160 --> 00:00:55,160
oh okay okay

11
00:01:14,799 --> 00:01:18,080
so are we doing everything online or

12
00:01:18,080 --> 00:01:21,040
after people are like you know attending

13
00:01:21,040 --> 00:01:24,600
again there are some members here

14
00:01:24,600 --> 00:01:24,600
oh okay okay

15
00:02:18,560 --> 00:02:23,000
can you hear me well or not

16
00:02:28,879 --> 00:02:32,640
there is an echo with a long delay

17
00:02:32,640 --> 00:02:37,640
you don't know

18
00:02:37,640 --> 00:02:37,640
so yeah we're waiting here for uh

19
00:02:41,519 --> 00:02:45,319
as soon as they are here

20
00:02:55,280 --> 00:02:58,000
okay

21
00:02:58,000 --> 00:03:01,640
we're trying to solve the problem do you

22
00:03:01,640 --> 00:03:01,640
hear nico now

23
00:03:07,200 --> 00:03:10,200
city

24
00:03:42,239 --> 00:03:45,239
oh

25
00:03:49,760 --> 00:03:56,439
so we are waiting for the dream members

26
00:03:56,439 --> 00:03:56,439
who are still having lunch

27
00:04:26,160 --> 00:04:29,160
hey

28
00:04:32,880 --> 00:04:35,880
oh

29
00:06:26,319 --> 00:06:29,319
two

30
00:06:40,160 --> 00:06:43,160
oh

31
00:06:48,960 --> 00:06:51,960
um

32
00:07:20,080 --> 00:07:23,080
yes

33
00:08:45,519 --> 00:08:48,519
oh

34
00:09:06,320 --> 00:09:12,760
the the dream members are here so we're

35
00:09:12,760 --> 00:09:12,760
gonna start

36
00:09:30,320 --> 00:09:33,320
foreign

37
00:09:46,800 --> 00:09:53,360
so good afternoon everyone

38
00:09:53,360 --> 00:09:55,680
welcome to the phd defense organization

39
00:09:55,680 --> 00:09:58,560
so the jury has selected

40
00:09:58,560 --> 00:10:00,160
a president who will be the master of

41
00:10:00,160 --> 00:10:02,000
ceremony for this

42
00:10:02,000 --> 00:10:05,680
phd defense

43
00:10:05,680 --> 00:10:07,440
and the president is uh rodolf

44
00:10:07,440 --> 00:10:11,200
and i will leave him

45
00:10:11,200 --> 00:10:12,800
the duty to present the jury and

46
00:10:12,800 --> 00:10:14,079
lead the

47
00:10:14,079 --> 00:10:16,079
the four

48
00:10:16,079 --> 00:10:18,560
events

49
00:10:18,560 --> 00:10:20,399
thank you very much um xavier and

50
00:10:20,399 --> 00:10:23,760
welcome to everyone

51
00:10:23,760 --> 00:10:25,200
um this is the public phd defense of jan

52
00:10:25,200 --> 00:10:26,800
tanwardas

53
00:10:26,800 --> 00:10:29,200
and um

54
00:10:29,200 --> 00:10:32,560
the jury we have a very distinguished

55
00:10:32,560 --> 00:10:34,160
set of international experts to evaluate

56
00:10:34,160 --> 00:10:37,040
the thesis

57
00:10:37,040 --> 00:10:38,079
so i will briefly introduce them

58
00:10:38,079 --> 00:10:39,360
first

59
00:10:39,360 --> 00:10:42,079
we have two

60
00:10:42,079 --> 00:10:44,640
reporters so the first one is kieran von

61
00:10:44,640 --> 00:10:46,480
absil who is professor at the university

62
00:10:46,480 --> 00:10:47,360
of louisville in belgium

63
00:10:47,360 --> 00:10:49,120
and

64
00:10:49,120 --> 00:10:51,279
professor mark arno don from the

65
00:10:51,279 --> 00:10:54,880
university of bordeaux

66
00:10:54,880 --> 00:10:56,240
and then we have external examiners

67
00:10:56,240 --> 00:10:57,680
including

68
00:10:57,680 --> 00:11:01,440
two of them

69
00:11:01,440 --> 00:11:03,279
in person so professor rajendra battia

70
00:11:03,279 --> 00:11:06,480
who is currently professor

71
00:11:06,480 --> 00:11:08,399
at ashoka university in india

72
00:11:08,399 --> 00:11:11,279
and then

73
00:11:11,279 --> 00:11:12,480
professor william lay who is emeritus

74
00:11:12,480 --> 00:11:14,079
professor at the university of

75
00:11:14,079 --> 00:11:18,160
nottingham

76
00:11:18,160 --> 00:11:21,040
and then we have three members online

77
00:11:21,040 --> 00:11:22,079
starting with minha wang who is

78
00:11:22,079 --> 00:11:24,640
associate

79
00:11:24,640 --> 00:11:26,959
researchers at the rican center

80
00:11:26,959 --> 00:11:29,680
for the advanced intelligence projects

81
00:11:29,680 --> 00:11:31,600
in tokyo

82
00:11:31,600 --> 00:11:36,399
frank nielsen

83
00:11:36,399 --> 00:11:37,760
likewise from tokyo a researcher at sony

84
00:11:37,760 --> 00:11:40,240
and

85
00:11:40,240 --> 00:11:43,120
myself a professor of engineering at the

86
00:11:43,120 --> 00:11:45,279
university of cambridge and of course

87
00:11:45,279 --> 00:11:47,839
last but not least the

88
00:11:47,839 --> 00:11:50,160
supervisor of the thesis who is xavier

89
00:11:50,160 --> 00:11:51,440
pennek from indriya at the social anti

90
00:11:51,440 --> 00:11:54,160
police who is

91
00:11:54,160 --> 00:11:55,040
in person and was just speaking a minute

92
00:11:55,040 --> 00:11:57,839
ago

93
00:11:57,839 --> 00:12:00,720
so without further delay we'll now hear

94
00:12:00,720 --> 00:12:03,120
the presentation of young tanwerdas you

95
00:12:03,120 --> 00:12:05,040
have about 45 minutes to explain the

96
00:12:05,040 --> 00:12:06,639
main outcome of your

97
00:12:06,639 --> 00:12:08,079
phd

98
00:12:08,079 --> 00:12:10,880
thank you

99
00:12:10,880 --> 00:12:15,760
thank you mr president

100
00:12:15,760 --> 00:12:15,760
so let me just start the presentation

101
00:12:22,560 --> 00:12:25,360
yeah

102
00:12:25,360 --> 00:12:28,320
so yeah i would like to thank all the

103
00:12:28,320 --> 00:12:31,440
dream members for being here or uh for

104
00:12:31,440 --> 00:12:32,959
following us uh brilliantly um

105
00:12:32,959 --> 00:12:34,800
thank you mr president for the

106
00:12:34,800 --> 00:12:37,680
introduction

107
00:12:37,680 --> 00:12:40,240
so my name is yantan vardas and i'm

108
00:12:40,240 --> 00:12:44,240
gonna present to you my phd work

109
00:12:44,240 --> 00:12:46,240
that i did during my my phd thesis

110
00:12:46,240 --> 00:12:47,360
which was supervised by professor xavier

111
00:12:47,360 --> 00:12:49,440
pinek

112
00:12:49,440 --> 00:12:51,600
and which is entitled romanian and

113
00:12:51,600 --> 00:12:53,920
stratified geometries of covariance and

114
00:12:53,920 --> 00:12:57,600
correlation matrices

115
00:12:57,600 --> 00:12:59,680
so this is quite a few strange words

116
00:12:59,680 --> 00:13:02,720
so what are covariance and correlation

117
00:13:02,720 --> 00:13:04,079
matrices where can we find them

118
00:13:04,079 --> 00:13:06,240
and

119
00:13:06,240 --> 00:13:08,880
why do we need to care about their

120
00:13:08,880 --> 00:13:11,120
geometry

121
00:13:11,120 --> 00:13:14,240
um so for example covariance matrices

122
00:13:14,240 --> 00:13:17,279
can be found in diffusion tensor imaging

123
00:13:17,279 --> 00:13:18,959
diffusion tensors are 3d

124
00:13:18,959 --> 00:13:20,880
symmetric positive semi-definite

125
00:13:20,880 --> 00:13:22,240
matrices

126
00:13:22,240 --> 00:13:24,079
which contain

127
00:13:24,079 --> 00:13:26,560
the diffusion coefficients of water

128
00:13:26,560 --> 00:13:29,200
molecules in the brain

129
00:13:29,200 --> 00:13:30,800
they are represented by 3d ellipsoids

130
00:13:30,800 --> 00:13:33,680
and

131
00:13:33,680 --> 00:13:35,519
the longest axis of the ellipsoid

132
00:13:35,519 --> 00:13:37,279
indicates the main direction of

133
00:13:37,279 --> 00:13:39,839
diffusion

134
00:13:39,839 --> 00:13:41,920
in electrons photography

135
00:13:41,920 --> 00:13:43,290
we can compute the covariance between

136
00:13:43,290 --> 00:13:44,560
two signals

137
00:13:44,560 --> 00:13:47,550
[Music]

138
00:13:47,550 --> 00:13:48,720
collected at two electrodes on the brain

139
00:13:48,720 --> 00:13:51,120
[Music]

140
00:13:51,120 --> 00:13:53,760
and all these covariance coefficients

141
00:13:53,760 --> 00:13:56,800
form a matrix that is called the

142
00:13:56,800 --> 00:13:56,800
covariance matrix

143
00:13:56,880 --> 00:14:02,320
we can also compare the correlation

144
00:14:02,320 --> 00:14:04,720
coefficient the correlation between two

145
00:14:04,720 --> 00:14:07,440
signals um

146
00:14:07,440 --> 00:14:08,800
independently from their scales

147
00:14:08,800 --> 00:14:10,560
um

148
00:14:10,560 --> 00:14:13,440
so the correlation coefficient is the

149
00:14:13,440 --> 00:14:15,680
covariance normalized by

150
00:14:15,680 --> 00:14:18,320
the standard deviation

151
00:14:18,320 --> 00:14:21,040
of the two signals and that's why they

152
00:14:21,040 --> 00:14:23,040
are between minus one and one

153
00:14:23,040 --> 00:14:24,240
as you can see in the correlation matrix

154
00:14:24,240 --> 00:14:25,600
here

155
00:14:25,600 --> 00:14:28,800
um

156
00:14:28,800 --> 00:14:31,279
and so the correlation matrix is the

157
00:14:31,279 --> 00:14:33,839
the the set of all uh correlation

158
00:14:33,839 --> 00:14:36,079
coefficients uh all together in a matrix

159
00:14:36,079 --> 00:14:38,399
so for example here it's a correlation

160
00:14:38,399 --> 00:14:40,639
matrix between brain regions in

161
00:14:40,639 --> 00:14:44,010
functional mri

162
00:14:44,010 --> 00:14:45,199
and it can also model uh other types of

163
00:14:45,199 --> 00:14:48,880
[Music]

164
00:14:48,880 --> 00:14:50,880
correlations between electrodes between

165
00:14:50,880 --> 00:14:54,399
genes cells

166
00:14:54,399 --> 00:14:54,399
or amino acids for example

167
00:14:54,959 --> 00:14:59,360
so more precisely how do we compute a

168
00:14:59,360 --> 00:15:01,440
covariance and the correlation matrix

169
00:15:01,440 --> 00:15:04,399
from raw data

170
00:15:04,399 --> 00:15:06,959
so given a set of signals

171
00:15:06,959 --> 00:15:09,519
we can sample

172
00:15:09,519 --> 00:15:11,680
the signal and remove the mean we get

173
00:15:11,680 --> 00:15:14,639
this matrix x

174
00:15:14,639 --> 00:15:16,959
then multiply by the transpose so we get

175
00:15:16,959 --> 00:15:18,639
the covariance matrix

176
00:15:18,639 --> 00:15:20,720
sigma

177
00:15:20,720 --> 00:15:23,839
and then we normalize by

178
00:15:23,839 --> 00:15:25,600
the standard deviations and we get this

179
00:15:25,600 --> 00:15:27,199
correlation matrix

180
00:15:27,199 --> 00:15:29,839
c

181
00:15:29,839 --> 00:15:31,839
and so we can already observe that

182
00:15:31,839 --> 00:15:35,279
in the covariance matrix

183
00:15:35,279 --> 00:15:37,920
there are n times n plus one over two

184
00:15:37,920 --> 00:15:39,600
three coefficients when where n is the

185
00:15:39,600 --> 00:15:42,560
dimension of the matrix

186
00:15:42,560 --> 00:15:44,320
because the matrix is symmetric

187
00:15:44,320 --> 00:15:47,519
and in the correlation matrix we have

188
00:15:47,519 --> 00:15:48,720
only n times n minus one over two three

189
00:15:48,720 --> 00:15:51,199
coefficients

190
00:15:51,199 --> 00:15:54,199
because the diagonal is fixed to one two

191
00:15:54,199 --> 00:15:54,199
identity

192
00:15:54,639 --> 00:15:56,800
um

193
00:15:56,800 --> 00:15:59,120
and these

194
00:15:59,120 --> 00:16:01,759
um the

195
00:16:01,759 --> 00:16:03,680
the positivity constraints on covariance

196
00:16:03,680 --> 00:16:07,199
and correlation matrix

197
00:16:07,199 --> 00:16:10,079
matrices uh make these spaces non-linear

198
00:16:10,079 --> 00:16:12,720
so the space of covariance matrices is a

199
00:16:12,720 --> 00:16:16,560
convex cone a closed convex cone in the

200
00:16:16,560 --> 00:16:16,560
vector space of symmetric matrices

201
00:16:16,720 --> 00:16:22,880
and symmetric positive definite matrices

202
00:16:22,880 --> 00:16:26,000
or spd matrices are located in the

203
00:16:26,000 --> 00:16:29,199
interior of the of the cone

204
00:16:29,199 --> 00:16:31,759
so they form an open convex clone

205
00:16:31,759 --> 00:16:34,399
and since it is open and convex it is

206
00:16:34,399 --> 00:16:38,959
diphomorphic to the vector space of same

207
00:16:38,959 --> 00:16:42,000
dimension n times n plus one over two

208
00:16:42,000 --> 00:16:46,079
uh and so here is represented the the

209
00:16:46,079 --> 00:16:46,079
the cone of two d covariance matrices

210
00:16:46,639 --> 00:16:52,000
the set of correlation matrices is a

211
00:16:52,000 --> 00:16:55,040
compact convex set in this vector space

212
00:16:55,040 --> 00:16:57,360
of symmetric matrices

213
00:16:57,360 --> 00:16:58,880
it's also compact and convex in its

214
00:16:58,880 --> 00:17:01,279
affine span

215
00:17:01,279 --> 00:17:04,480
which is the affine space of symmetric

216
00:17:04,480 --> 00:17:07,199
matrices with identity diagonal

217
00:17:07,199 --> 00:17:09,760
which is denoted here identity plus

218
00:17:09,760 --> 00:17:14,319
whole n where pole n is the vector space

219
00:17:14,319 --> 00:17:15,679
of symmetric matrices with new diagonal

220
00:17:15,679 --> 00:17:17,360
and so the full rank correlation

221
00:17:17,360 --> 00:17:21,439
matrices are located

222
00:17:21,439 --> 00:17:23,520
inside so in the interior of the

223
00:17:23,520 --> 00:17:25,600
of the ellipto this space is called the

224
00:17:25,600 --> 00:17:28,720
ellipto

225
00:17:28,720 --> 00:17:32,400
and so they form an open convex set in

226
00:17:32,400 --> 00:17:32,400
the fine spell space

227
00:17:32,640 --> 00:17:38,080
of symmetric matrices with identity

228
00:17:38,080 --> 00:17:40,000
diagonal and since it is open and convex

229
00:17:40,000 --> 00:17:41,280
there exists a different morphism

230
00:17:41,280 --> 00:17:44,000
towards

231
00:17:44,000 --> 00:17:46,799
the vector space of dimension n times n

232
00:17:46,799 --> 00:17:48,640
minus 1 over 2.

233
00:17:48,640 --> 00:17:53,280
and so here is the

234
00:17:53,280 --> 00:17:53,280
the set of 3d correlation matrices

235
00:17:54,240 --> 00:17:58,240
and so these spaces are not vector

236
00:17:58,240 --> 00:18:00,400
spaces so covariance and correlation

237
00:18:00,400 --> 00:18:02,160
matrices belong to the family of

238
00:18:02,160 --> 00:18:03,840
non-linear data

239
00:18:03,840 --> 00:18:08,080
as well as trees

240
00:18:08,080 --> 00:18:08,080
meshes scandal shapes for example

241
00:18:08,480 --> 00:18:13,200
the for example the set of

242
00:18:13,200 --> 00:18:15,039
philogenetic trees

243
00:18:15,039 --> 00:18:17,360
is an orphan space

244
00:18:17,360 --> 00:18:19,360
uh the the positive orthot in a vector

245
00:18:19,360 --> 00:18:22,080
space is the set of vectors with all

246
00:18:22,080 --> 00:18:24,240
positive coordinates and so an earth and

247
00:18:24,240 --> 00:18:27,360
space is a set of orthons

248
00:18:27,360 --> 00:18:30,559
glued together in a certain way

249
00:18:30,559 --> 00:18:31,679
so the question is uh how can we compute

250
00:18:31,679 --> 00:18:34,880
in these

251
00:18:34,880 --> 00:18:37,520
spaces for example how how

252
00:18:37,520 --> 00:18:39,840
can i interpolate between two trees uh

253
00:18:39,840 --> 00:18:42,240
in this space

254
00:18:42,240 --> 00:18:44,880
how can we define probability

255
00:18:44,880 --> 00:18:48,720
distributions on the spaces of

256
00:18:48,720 --> 00:18:51,600
covariance and correlation matrices

257
00:18:51,600 --> 00:18:54,080
and so to answer all these questions we

258
00:18:54,080 --> 00:18:57,760
need to formalize a bit more the

259
00:18:57,760 --> 00:18:59,600
spaces we will work in

260
00:18:59,600 --> 00:19:02,640
so i will talk about three types of

261
00:19:02,640 --> 00:19:05,120
spaces the vector spaces the manifolds

262
00:19:05,120 --> 00:19:07,520
and the stratified spaces

263
00:19:07,520 --> 00:19:11,919
a vector space is a space that is stable

264
00:19:11,919 --> 00:19:14,160
by addition difference and scaling

265
00:19:14,160 --> 00:19:16,000
a manifold is a space that can be

266
00:19:16,000 --> 00:19:18,320
locally approximated

267
00:19:18,320 --> 00:19:21,360
by a vector space called the tangent

268
00:19:21,360 --> 00:19:23,679
space at each point

269
00:19:23,679 --> 00:19:25,760
so for example vector spaces are

270
00:19:25,760 --> 00:19:28,799
manifolds trivial examples of many

271
00:19:28,799 --> 00:19:31,840
faults open sets of vector spaces are

272
00:19:31,840 --> 00:19:34,080
also many folds such as the set of

273
00:19:34,080 --> 00:19:36,720
positive real numbers or the set of spd

274
00:19:36,720 --> 00:19:36,720
matrices

275
00:19:37,679 --> 00:19:42,640
and we can also cite the famous example

276
00:19:42,640 --> 00:19:46,000
of the sphere which is a manifold

277
00:19:46,000 --> 00:19:48,320
and then a stratified space uh is a set

278
00:19:48,320 --> 00:19:49,760
of several manifolds glued together in a

279
00:19:49,760 --> 00:19:53,280
certain way

280
00:19:53,280 --> 00:19:56,000
for example the space of non-negative

281
00:19:56,000 --> 00:19:58,080
real numbers is a stratified space

282
00:19:58,080 --> 00:20:01,120
uh because it is composed of too many

283
00:20:01,120 --> 00:20:04,159
faults the many fold of positive numbers

284
00:20:04,159 --> 00:20:06,960
and the trivial manifold with the zero

285
00:20:06,960 --> 00:20:09,919
with only zero and so the cube and the

286
00:20:09,919 --> 00:20:14,159
spider are also examples of stratified

287
00:20:14,159 --> 00:20:16,240
spaces so let me develop this uh idea of

288
00:20:16,240 --> 00:20:18,400
stratified space on

289
00:20:18,400 --> 00:20:20,400
the cone of covariance matrices so the

290
00:20:20,400 --> 00:20:24,400
cone of covariance matrices

291
00:20:24,400 --> 00:20:26,799
is a stratified space with three strata

292
00:20:26,799 --> 00:20:28,240
the first atom is composed of the zero

293
00:20:28,240 --> 00:20:30,720
matrix

294
00:20:30,720 --> 00:20:36,000
the only matrix of rank zero

295
00:20:36,000 --> 00:20:38,799
then we can add the matrices of rank one

296
00:20:38,799 --> 00:20:40,960
and we get the the boundary of the tone

297
00:20:40,960 --> 00:20:44,080
and then we can add the rank two

298
00:20:44,080 --> 00:20:46,240
matrices the the spd matrices and we get

299
00:20:46,240 --> 00:20:49,039
the interior of the cone and so we get

300
00:20:49,039 --> 00:20:51,039
the whole uh the whole space of

301
00:20:51,039 --> 00:20:53,120
covariance matrices two deco various

302
00:20:53,120 --> 00:20:56,320
matrices here

303
00:20:56,320 --> 00:20:57,440
and so the the space of spd matrices is

304
00:20:57,440 --> 00:20:59,679
dense

305
00:20:59,679 --> 00:21:02,679
uh in the in the space of covariance

306
00:21:02,679 --> 00:21:02,679
matrices

307
00:21:03,840 --> 00:21:08,159
so how do we

308
00:21:08,159 --> 00:21:09,640
generalize the classical linear

309
00:21:09,640 --> 00:21:10,880
operations

310
00:21:10,880 --> 00:21:13,120
[Music]

311
00:21:13,120 --> 00:21:15,200
from euclidean spaces to these other

312
00:21:15,200 --> 00:21:18,559
non-linear spaces

313
00:21:18,559 --> 00:21:21,120
euclidean geometry is based on the

314
00:21:21,120 --> 00:21:24,240
the choice of an inner product

315
00:21:24,240 --> 00:21:26,880
for example the gradient changes if the

316
00:21:26,880 --> 00:21:30,080
inner product changes

317
00:21:30,080 --> 00:21:33,440
uh on a manifold the analogous

318
00:21:33,440 --> 00:21:35,360
um basic geometric operation uh is the

319
00:21:35,360 --> 00:21:37,840
remaining metric

320
00:21:37,840 --> 00:21:40,159
it is an inner product on each tangent

321
00:21:40,159 --> 00:21:44,400
space

322
00:21:44,400 --> 00:21:47,120
and so many european operations uh can

323
00:21:47,120 --> 00:21:50,320
be generalized to romanian manifolds

324
00:21:50,320 --> 00:21:52,320
such as the geodesics the the gradient

325
00:21:52,320 --> 00:21:55,280
the the measure

326
00:21:55,280 --> 00:21:57,679
and some new notions appear such as

327
00:21:57,679 --> 00:22:00,320
the curvature

328
00:22:00,320 --> 00:22:03,840
the stratified space we usually define a

329
00:22:03,840 --> 00:22:07,520
distance which is a more general notion

330
00:22:07,520 --> 00:22:09,919
but which conveys much less

331
00:22:09,919 --> 00:22:09,919
geometric

332
00:22:10,840 --> 00:22:16,400
operations and so these geometric

333
00:22:16,400 --> 00:22:19,200
operations are the building blocks

334
00:22:19,200 --> 00:22:20,000
to perform statistical operations

335
00:22:20,000 --> 00:22:22,640
um

336
00:22:22,640 --> 00:22:24,720
so many romanian

337
00:22:24,720 --> 00:22:26,400
algorithms and results

338
00:22:26,400 --> 00:22:28,000
extend the

339
00:22:28,000 --> 00:22:30,080
the usual ones

340
00:22:30,080 --> 00:22:32,960
in euclidean spaces

341
00:22:32,960 --> 00:22:37,919
as you can see here

342
00:22:37,919 --> 00:22:41,280
and in on the contrary in metric spaces

343
00:22:41,280 --> 00:22:43,679
there exists fewer general

344
00:22:43,679 --> 00:22:45,760
algorithms and results

345
00:22:45,760 --> 00:22:48,880
and so on stratified spaces in the

346
00:22:48,880 --> 00:22:51,280
literature uh the

347
00:22:51,280 --> 00:22:55,520
the the methods that are used are mainly

348
00:22:55,520 --> 00:22:59,280
defined uh on specifically on on

349
00:22:59,280 --> 00:22:59,280
examples of spaces

350
00:23:00,159 --> 00:23:06,159
so now let me convince you that the

351
00:23:06,159 --> 00:23:08,640
geometry uh changes the the statistics

352
00:23:08,640 --> 00:23:10,480
and the data analysis

353
00:23:10,480 --> 00:23:11,919
with two examples

354
00:23:11,919 --> 00:23:14,640
so on the left

355
00:23:14,640 --> 00:23:18,000
we you can see the the regularization of

356
00:23:18,000 --> 00:23:18,000
the dti slice

357
00:23:18,640 --> 00:23:23,840
around the corpus callosum in the brain

358
00:23:23,840 --> 00:23:27,600
and so the first image is the raw

359
00:23:27,600 --> 00:23:30,000
estimation of diffusion tensors

360
00:23:30,000 --> 00:23:31,600
the second one shows a euclidean

361
00:23:31,600 --> 00:23:33,760
regularization

362
00:23:33,760 --> 00:23:35,520
of the tensors

363
00:23:35,520 --> 00:23:37,360
and the third one shows a romanian

364
00:23:37,360 --> 00:23:38,960
regularization

365
00:23:38,960 --> 00:23:40,960
and you can see that the remaining

366
00:23:40,960 --> 00:23:44,640
regularization

367
00:23:44,640 --> 00:23:48,000
is smoother than the ukn1

368
00:23:48,000 --> 00:23:48,000
and without outliers

369
00:23:48,240 --> 00:23:56,400
on the right maybe even more striking uh

370
00:23:56,400 --> 00:23:58,240
you can see an example of classification

371
00:23:58,240 --> 00:24:00,640
uh between left-hand movement and

372
00:24:00,640 --> 00:24:03,039
right-hand movement in brain computer

373
00:24:03,039 --> 00:24:05,520
interfaces

374
00:24:05,520 --> 00:24:07,360
and the the the use of the romanian

375
00:24:07,360 --> 00:24:09,200
metric was shown

376
00:24:09,200 --> 00:24:10,960
to separate

377
00:24:10,960 --> 00:24:16,760
much more accurately

378
00:24:16,760 --> 00:24:16,760
the two classes than the use of a euv

379
00:24:18,159 --> 00:24:24,080
so the geometry determines the the

380
00:24:24,080 --> 00:24:26,799
statistics and the data analysis

381
00:24:26,799 --> 00:24:29,120
however there is not a canonical choice

382
00:24:29,120 --> 00:24:30,720
of a geometric structure on

383
00:24:30,720 --> 00:24:32,480
uh covariance and

384
00:24:32,480 --> 00:24:34,880
relation matrices and ordinary in these

385
00:24:34,880 --> 00:24:35,919
non-linear spaces

386
00:24:35,919 --> 00:24:39,360
so

387
00:24:39,360 --> 00:24:40,640
which principles should we follow to

388
00:24:40,640 --> 00:24:43,279
um

389
00:24:43,279 --> 00:24:44,880
to choose the the geometric structure on

390
00:24:44,880 --> 00:24:48,640
our

391
00:24:48,640 --> 00:24:50,799
nonlinear spaces so in my thesis i

392
00:24:50,799 --> 00:24:53,120
proposed three principles

393
00:24:53,120 --> 00:24:55,520
uh the first principle is the adequacy

394
00:24:55,520 --> 00:24:56,880
of the model to the data

395
00:24:56,880 --> 00:24:58,880
so for example

396
00:24:58,880 --> 00:25:00,799
we can require that the geometric

397
00:25:00,799 --> 00:25:03,200
structure is invariant

398
00:25:03,200 --> 00:25:05,440
under a group of transformations if

399
00:25:05,440 --> 00:25:08,880
these transformations should not

400
00:25:08,880 --> 00:25:11,120
influence our data analysis

401
00:25:11,120 --> 00:25:15,000
i will develop this later

402
00:25:15,000 --> 00:25:15,000
on covariance matrices

403
00:25:15,360 --> 00:25:18,240
the second

404
00:25:18,240 --> 00:25:20,880
principle is to have good theoretical

405
00:25:20,880 --> 00:25:23,440
properties with our space so here i

406
00:25:23,440 --> 00:25:24,559
wrote two sequences of geometric

407
00:25:24,559 --> 00:25:27,120
structures

408
00:25:27,120 --> 00:25:31,039
such that the ones on the right

409
00:25:31,039 --> 00:25:31,039
have more properties than on the left

410
00:25:31,440 --> 00:25:35,679
and these good theoretical properties

411
00:25:35,679 --> 00:25:37,200
come often come with good practical

412
00:25:37,200 --> 00:25:39,520
properties

413
00:25:39,520 --> 00:25:42,559
such as closed form formulae and

414
00:25:42,559 --> 00:25:44,080
efficient algorithms to implement in

415
00:25:44,080 --> 00:25:46,559
libraries

416
00:25:46,559 --> 00:25:49,760
such as geostats to compute on

417
00:25:49,760 --> 00:25:49,760
non-linear manifolds

418
00:25:50,000 --> 00:25:54,640
and such requirements often define

419
00:25:54,640 --> 00:25:58,000
families of geometries rather than a

420
00:25:58,000 --> 00:26:00,480
unique geometric structure on our

421
00:26:00,480 --> 00:26:04,400
spaces

422
00:26:04,400 --> 00:26:06,960
so i will apply all these principles

423
00:26:06,960 --> 00:26:09,600
to the the spaces of covariance and

424
00:26:09,600 --> 00:26:11,840
correlation matrices

425
00:26:11,840 --> 00:26:14,960
in the first part i will focus on the

426
00:26:14,960 --> 00:26:17,120
manifold of spd matrices

427
00:26:17,120 --> 00:26:18,720
i will show you that there are many

428
00:26:18,720 --> 00:26:21,440
existing remaining metrics in the

429
00:26:21,440 --> 00:26:23,440
literature and so i will use the

430
00:26:23,440 --> 00:26:26,799
previous principles to propose a

431
00:26:26,799 --> 00:26:31,279
classification of these romanian metrics

432
00:26:31,279 --> 00:26:31,279
into families with general properties

433
00:26:31,600 --> 00:26:35,919
in the second part i will turn to the

434
00:26:35,919 --> 00:26:39,120
manifold of full rank correlation

435
00:26:39,120 --> 00:26:41,520
matrices and here there are

436
00:26:41,520 --> 00:26:43,120
very few existing remaining metrics in

437
00:26:43,120 --> 00:26:46,159
the literature

438
00:26:46,159 --> 00:26:48,640
but we saw previously that this space

439
00:26:48,640 --> 00:26:53,039
so the interior of the ellipto is the

440
00:26:53,039 --> 00:26:54,799
theomorphic to the vector space and so

441
00:26:54,799 --> 00:26:56,640
i will propose several different

442
00:26:56,640 --> 00:26:59,440
morphemes towards

443
00:26:59,440 --> 00:27:02,080
vector spaces

444
00:27:02,080 --> 00:27:05,440
to define log euclidean metrics

445
00:27:05,440 --> 00:27:08,320
on full rank correlation matrices

446
00:27:08,320 --> 00:27:10,720
and in the third part uh i will focus on

447
00:27:10,720 --> 00:27:12,400
the stratified space of covariance

448
00:27:12,400 --> 00:27:14,320
matrices

449
00:27:14,320 --> 00:27:16,799
so the pure versus time distance is

450
00:27:16,799 --> 00:27:20,159
defined globally on this space but it

451
00:27:20,159 --> 00:27:23,919
was only studied on each stratum and so

452
00:27:23,919 --> 00:27:23,919
i will give you the global picture

453
00:27:26,240 --> 00:27:31,679
so let's start by classifying remaining

454
00:27:31,679 --> 00:27:34,000
matrix on spd matrices let me show you

455
00:27:34,000 --> 00:27:36,799
that there are many romanian metrics and

456
00:27:36,799 --> 00:27:39,520
then we will use i chose two principles

457
00:27:39,520 --> 00:27:42,480
for this presentation to classify these

458
00:27:42,480 --> 00:27:42,480
romanian metrics

459
00:27:44,000 --> 00:27:51,200
so until 2005 2006 uh the the euclidean

460
00:27:51,200 --> 00:27:53,679
metric was the the one that was mainly

461
00:27:53,679 --> 00:27:55,679
used in applications to classify

462
00:27:55,679 --> 00:27:57,679
covariance matrices or to do operations

463
00:27:57,679 --> 00:28:00,320
with covariance matrices

464
00:28:00,320 --> 00:28:03,120
and the offline invariant metric

465
00:28:03,120 --> 00:28:05,360
was shown to outperform many

466
00:28:05,360 --> 00:28:08,020
classification results and was better

467
00:28:08,020 --> 00:28:09,120
suited to compute with

468
00:28:09,120 --> 00:28:10,720
[Music]

469
00:28:10,720 --> 00:28:12,399
variance matrices

470
00:28:12,399 --> 00:28:13,840
then the log euclidean metric was

471
00:28:13,840 --> 00:28:15,679
proposed

472
00:28:15,679 --> 00:28:19,120
as another alternative and then there

473
00:28:19,120 --> 00:28:21,360
was an explosion of proposals on

474
00:28:21,360 --> 00:28:22,640
the the remaining metrics that we could

475
00:28:22,640 --> 00:28:25,760
use

476
00:28:25,760 --> 00:28:28,640
on spd matrices

477
00:28:28,640 --> 00:28:30,399
so maybe a first

478
00:28:30,399 --> 00:28:32,399
element of classification

479
00:28:32,399 --> 00:28:34,799
is to note that

480
00:28:34,799 --> 00:28:37,039
uh almost all of them are invariant

481
00:28:37,039 --> 00:28:40,799
under the the action of the orthogonal

482
00:28:40,799 --> 00:28:43,679
group so i will explain what it is

483
00:28:43,679 --> 00:28:45,600
um and so first i would like to present

484
00:28:45,600 --> 00:28:47,279
the the four main

485
00:28:47,279 --> 00:28:49,440
metrics

486
00:28:49,440 --> 00:28:51,679
the european the affine invariant the

487
00:28:51,679 --> 00:28:54,880
log euclidean and the overseas time

488
00:28:54,880 --> 00:28:57,520
matrix on spd matrices so we can see

489
00:28:57,520 --> 00:29:00,000
that they are different from the the

490
00:29:00,000 --> 00:29:02,159
formula of their distance

491
00:29:02,159 --> 00:29:05,039
distances romanian matrix

492
00:29:05,039 --> 00:29:07,360
and the formula of the mean

493
00:29:07,360 --> 00:29:09,360
and so to give some intuition the fine

494
00:29:09,360 --> 00:29:10,399
invariant metric and the log nutrient

495
00:29:10,399 --> 00:29:12,960
metric

496
00:29:12,960 --> 00:29:14,559
reject all the rank deficient matrices

497
00:29:14,559 --> 00:29:17,840
to infinity so

498
00:29:17,840 --> 00:29:20,559
these matrices are not reachable from uh

499
00:29:20,559 --> 00:29:21,919
spd matrices

500
00:29:21,919 --> 00:29:24,480
on the contrary the view of a search

501
00:29:24,480 --> 00:29:27,600
time metric keeps these rank deficient

502
00:29:27,600 --> 00:29:30,000
matrices at finite distance

503
00:29:30,000 --> 00:29:30,000
and so

504
00:29:30,320 --> 00:29:33,360
that's

505
00:29:33,360 --> 00:29:34,240
an important difference between between

506
00:29:34,240 --> 00:29:36,240
them

507
00:29:36,240 --> 00:29:40,159
and so you can see

508
00:29:40,159 --> 00:29:42,640
an interpolation between two

509
00:29:42,640 --> 00:29:44,480
covariance matrices there's pd matrices

510
00:29:44,480 --> 00:29:46,720
with the four

511
00:29:46,720 --> 00:29:48,799
remaining metrics

512
00:29:48,799 --> 00:29:52,640
and you can see that they give different

513
00:29:52,640 --> 00:29:52,640
results in this interpolation

514
00:29:53,760 --> 00:29:58,480
so let's apply our first principle uh

515
00:29:58,480 --> 00:29:59,760
the invariance and the regroup action so

516
00:29:59,760 --> 00:30:02,559
the natural

517
00:30:02,559 --> 00:30:05,200
uh action on covariance matrices

518
00:30:05,200 --> 00:30:07,760
uh is the congruence action of the

519
00:30:07,760 --> 00:30:10,320
affine group which reduces to the

520
00:30:10,320 --> 00:30:13,279
congruent section of the general linear

521
00:30:13,279 --> 00:30:14,240
group it comes from the action of

522
00:30:14,240 --> 00:30:16,399
the

523
00:30:16,399 --> 00:30:19,279
the define move

524
00:30:19,279 --> 00:30:21,200
on random vectors and so then when we

525
00:30:21,200 --> 00:30:22,799
look at what it gives on the covariance

526
00:30:22,799 --> 00:30:26,240
matrix

527
00:30:26,240 --> 00:30:28,799
we get this formula with a sigma a

528
00:30:28,799 --> 00:30:28,799
transpose

529
00:30:29,360 --> 00:30:33,520
and so all the fine invariant metrics

530
00:30:33,520 --> 00:30:35,679
were characterized

531
00:30:35,679 --> 00:30:38,720
by this formula

532
00:30:38,720 --> 00:30:41,919
with two parameters

533
00:30:41,919 --> 00:30:44,799
they endo the the space of

534
00:30:44,799 --> 00:30:46,399
spd matrices with a remaining symmetric

535
00:30:46,399 --> 00:30:48,240
structure

536
00:30:48,240 --> 00:30:50,000
the singular matrices are rejected to

537
00:30:50,000 --> 00:30:54,640
infinity and we have closed form

538
00:30:54,640 --> 00:30:54,640
formulae for old geometric operations

539
00:30:54,880 --> 00:31:01,440
and so a natural question is to

540
00:31:01,440 --> 00:31:05,120
try to characterize the metrics that are

541
00:31:05,120 --> 00:31:06,960
invariant under subgroups of the general

542
00:31:06,960 --> 00:31:10,240
linear group

543
00:31:10,240 --> 00:31:13,519
so here are several

544
00:31:13,519 --> 00:31:15,760
subgroups of the general linear group

545
00:31:15,760 --> 00:31:16,960
and so the for example if we require

546
00:31:16,960 --> 00:31:19,200
that our

547
00:31:19,200 --> 00:31:20,240
geometry is invariant under permutations

548
00:31:20,240 --> 00:31:22,960
it means

549
00:31:22,960 --> 00:31:24,960
that um

550
00:31:24,960 --> 00:31:28,399
we don't um

551
00:31:28,399 --> 00:31:32,559
that the order that we chose uh

552
00:31:32,559 --> 00:31:33,600
between the entries is arbitrary

553
00:31:33,600 --> 00:31:35,440
so this

554
00:31:35,440 --> 00:31:39,200
diagram is

555
00:31:39,200 --> 00:31:40,720
quite easy to obtain from the the

556
00:31:40,720 --> 00:31:43,120
subgroups the inclusions and

557
00:31:43,120 --> 00:31:46,159
intersections

558
00:31:46,159 --> 00:31:48,399
and so each group defines a family of

559
00:31:48,399 --> 00:31:50,880
remaining metrics that are invariant and

560
00:31:50,880 --> 00:31:54,640
they're this group

561
00:31:54,640 --> 00:31:57,120
so here is the the first prm saying that

562
00:31:57,120 --> 00:31:59,519
all permutation invariant metrics that

563
00:31:59,519 --> 00:32:01,919
are also invariant under

564
00:32:01,919 --> 00:32:05,120
the group of lower triangular matrices

565
00:32:05,120 --> 00:32:05,120
with positive diagonal

566
00:32:05,440 --> 00:32:08,640
are

567
00:32:08,640 --> 00:32:11,600
invariant under the general linear group

568
00:32:11,600 --> 00:32:13,679
so our defining variant

569
00:32:13,679 --> 00:32:14,480
i also characterized

570
00:32:14,480 --> 00:32:15,840
the

571
00:32:15,840 --> 00:32:18,559
continuous

572
00:32:18,559 --> 00:32:20,880
o of any invariant matrix and the lt

573
00:32:20,880 --> 00:32:25,120
plus invariant matrix

574
00:32:25,120 --> 00:32:25,120
that's detailed in the demand script

575
00:32:26,240 --> 00:32:32,159
so let's turn to our second principle uh

576
00:32:32,159 --> 00:32:34,640
the principle of jewelry flat manifolds

577
00:32:34,640 --> 00:32:36,159
um a dually flat manifold is a remaining

578
00:32:36,159 --> 00:32:38,159
manifold

579
00:32:38,159 --> 00:32:40,640
equipped with two flat torsion-free

580
00:32:40,640 --> 00:32:42,880
connections nablan and blastar that

581
00:32:42,880 --> 00:32:45,840
satisfy a condition of duality with

582
00:32:45,840 --> 00:32:46,880
respect to the metric

583
00:32:46,880 --> 00:32:48,559
and so

584
00:32:48,559 --> 00:32:50,000
this geometry is very interesting

585
00:32:50,000 --> 00:32:51,840
because

586
00:32:51,840 --> 00:32:53,840
it provides a canonical bregman

587
00:32:53,840 --> 00:32:57,039
divergence

588
00:32:57,039 --> 00:32:59,519
a divergence is a dissimilarity measure

589
00:32:59,519 --> 00:33:02,159
that looks like a distance but without

590
00:33:02,159 --> 00:33:05,279
the symmetry condition and the

591
00:33:05,279 --> 00:33:07,039
triangular inequality condition

592
00:33:07,039 --> 00:33:10,720
and so we can generalize the notion of

593
00:33:10,720 --> 00:33:11,440
mean into the notion of centuries

594
00:33:11,440 --> 00:33:13,039
and

595
00:33:13,039 --> 00:33:14,960
with this

596
00:33:14,960 --> 00:33:16,880
dissimilarity measure we can perform

597
00:33:16,880 --> 00:33:20,240
some operations that we can do with the

598
00:33:20,240 --> 00:33:21,200
distance such as clustering

599
00:33:21,200 --> 00:33:23,360
so

600
00:33:23,360 --> 00:33:24,880
maybe

601
00:33:24,880 --> 00:33:26,480
an example on

602
00:33:26,480 --> 00:33:28,640
divergences the pullback label

603
00:33:28,640 --> 00:33:30,159
divergence is probably the

604
00:33:30,159 --> 00:33:33,519
one of the

605
00:33:33,519 --> 00:33:36,080
well-known divergences so here on the

606
00:33:36,080 --> 00:33:37,919
the set of positive vectors

607
00:33:37,919 --> 00:33:39,440
so actually behind the back layer

608
00:33:39,440 --> 00:33:40,960
divergence there is a really flat

609
00:33:40,960 --> 00:33:44,000
manifold structure

610
00:33:44,000 --> 00:33:46,880
with the metric written here and

611
00:33:46,880 --> 00:33:50,000
the two connections uh the euclidean and

612
00:33:50,000 --> 00:33:52,080
the login connections

613
00:33:52,080 --> 00:33:54,960
and so these pullback regular divergence

614
00:33:54,960 --> 00:33:58,480
belongs to a more generic family uh the

615
00:33:58,480 --> 00:33:58,480
family of alphabet

616
00:33:59,840 --> 00:34:03,440
divergences

617
00:34:03,440 --> 00:34:05,360
uh so if you take alpha equals one and

618
00:34:05,360 --> 00:34:07,360
beta tends to zero in this formula you

619
00:34:07,360 --> 00:34:09,040
will retrieve the combat level of

620
00:34:09,040 --> 00:34:11,919
divergence

621
00:34:11,919 --> 00:34:17,119
and so these alpha beta divergences they

622
00:34:17,119 --> 00:34:17,119
can be defined also on spd matrices

623
00:34:17,599 --> 00:34:23,119
and so behind the these

624
00:34:23,119 --> 00:34:25,760
these divergences

625
00:34:25,760 --> 00:34:27,919
there is a dual flat manifold structure

626
00:34:27,919 --> 00:34:30,639
and so i defined the mixed power

627
00:34:30,639 --> 00:34:34,000
euclidean matrix as the matrix

628
00:34:34,000 --> 00:34:36,480
corresponding to these divergences

629
00:34:36,480 --> 00:34:38,800
and the the interesting point with this

630
00:34:38,800 --> 00:34:40,879
uh family of metric is that it

631
00:34:40,879 --> 00:34:43,520
encompasses uh

632
00:34:43,520 --> 00:34:45,919
many uh romanian metrics that we saw

633
00:34:45,919 --> 00:34:48,560
before that you for example the european

634
00:34:48,560 --> 00:34:51,359
metric defining variant metric the

635
00:34:51,359 --> 00:34:52,879
logically network and the others

636
00:34:52,879 --> 00:34:54,639
so it gives some structure some

637
00:34:54,639 --> 00:34:56,399
structure to

638
00:34:56,399 --> 00:34:58,800
the

639
00:34:58,800 --> 00:35:01,520
the set of remaining metrics on the

640
00:35:01,520 --> 00:35:04,000
manifold of spd matrices

641
00:35:04,000 --> 00:35:07,040
um i i derived

642
00:35:07,040 --> 00:35:09,280
the formula of the curvature uh i proved

643
00:35:09,280 --> 00:35:11,520
that there is always non-positive

644
00:35:11,520 --> 00:35:13,440
curvature and i prove experimentally

645
00:35:13,440 --> 00:35:17,839
that there are there is always

646
00:35:17,839 --> 00:35:22,560
non-negative curvature um

647
00:35:22,560 --> 00:35:22,560
on with all these these metrics

648
00:35:22,880 --> 00:35:27,119
so to summarize

649
00:35:27,119 --> 00:35:29,680
i characterized several classes of

650
00:35:29,680 --> 00:35:32,079
invariant metrics with respect to the

651
00:35:32,079 --> 00:35:33,680
group of invariants

652
00:35:33,680 --> 00:35:35,440
i defined these

653
00:35:35,440 --> 00:35:37,520
mixed euclidean metrics that are a bit

654
00:35:37,520 --> 00:35:38,560
more general than the mixed power eupdm

655
00:35:38,560 --> 00:35:39,520
matrix

656
00:35:39,520 --> 00:35:43,760
and i

657
00:35:43,760 --> 00:35:46,880
computed i derived the the curvature

658
00:35:46,880 --> 00:35:49,359
of for all these metrics

659
00:35:49,359 --> 00:35:51,839
and so this gives a hierarchy of

660
00:35:51,839 --> 00:35:54,079
families of romanian metrics

661
00:35:54,079 --> 00:35:56,640
and each time

662
00:35:56,640 --> 00:35:58,800
you know some general operations in a

663
00:35:58,800 --> 00:36:01,520
family you can implement it

664
00:36:01,520 --> 00:36:02,790
and as soon as you know a better way to

665
00:36:02,790 --> 00:36:04,079
compute the

666
00:36:04,079 --> 00:36:05,040
[Music]

667
00:36:05,040 --> 00:36:07,040
any

668
00:36:07,040 --> 00:36:10,400
for example the curvature

669
00:36:10,400 --> 00:36:12,640
you can specify a more

670
00:36:12,640 --> 00:36:15,040
efficient algorithm and so this gives a

671
00:36:15,040 --> 00:36:18,800
hierarchy of implementations in

672
00:36:18,800 --> 00:36:18,800
libraries such as geostats

673
00:36:19,599 --> 00:36:23,359
another observation is that the vueva

674
00:36:23,359 --> 00:36:25,359
sustain metric that i mentioned

675
00:36:25,359 --> 00:36:28,720
previously does not fit in this

676
00:36:28,720 --> 00:36:31,040
framework of mixed european metrics

677
00:36:31,040 --> 00:36:34,640
and so i will explain it

678
00:36:34,640 --> 00:36:38,079
more precisely in part three

679
00:36:38,079 --> 00:36:42,640
so these works uh gave rise to several

680
00:36:42,640 --> 00:36:42,640
publications that are written here

681
00:36:44,480 --> 00:36:49,200
so now let's turn to

682
00:36:49,200 --> 00:36:51,200
the manifold of full rank correlation

683
00:36:51,200 --> 00:36:54,240
matrices so we go from covariance

684
00:36:54,240 --> 00:36:56,640
matrices to correlation matrices i will

685
00:36:56,640 --> 00:36:59,040
show you that uh there is

686
00:36:59,040 --> 00:37:01,920
uh very very few

687
00:37:01,920 --> 00:37:04,240
metrics and so i will uh develop this

688
00:37:04,240 --> 00:37:08,079
quotient of fine metric that was defined

689
00:37:08,079 --> 00:37:09,760
actually uh during my thesis

690
00:37:09,760 --> 00:37:13,280
and then i will

691
00:37:13,280 --> 00:37:15,599
define some other metrics that are more

692
00:37:15,599 --> 00:37:18,079
convenient that are called logarithm

693
00:37:18,079 --> 00:37:18,079
metrics

694
00:37:19,280 --> 00:37:24,079
so the quotient defined metric

695
00:37:24,079 --> 00:37:25,119
was introduced by paul david in his

696
00:37:25,119 --> 00:37:25,390
thesis

697
00:37:25,390 --> 00:37:27,040
um

698
00:37:27,040 --> 00:37:29,920
[Music]

699
00:37:29,920 --> 00:37:29,920
it um

700
00:37:30,480 --> 00:37:33,200
yeah

701
00:37:33,200 --> 00:37:36,400
the the so the the space of correlation

702
00:37:36,400 --> 00:37:39,280
matrices can be seen as the quotient of

703
00:37:39,280 --> 00:37:41,599
the space of spd matrices

704
00:37:41,599 --> 00:37:43,440
by uh the positive diagonal group

705
00:37:43,440 --> 00:37:45,359
because we said that the correlation

706
00:37:45,359 --> 00:37:48,960
matrices are normalized

707
00:37:48,960 --> 00:37:51,599
uh versions of covariance matrices

708
00:37:51,599 --> 00:37:54,160
and so any remaining metric that is

709
00:37:54,160 --> 00:37:57,440
invariant under the positive diagonal

710
00:37:57,440 --> 00:37:58,880
group on spd matrices descends to the

711
00:37:58,880 --> 00:38:02,320
remaining metric

712
00:38:02,320 --> 00:38:05,200
on foreign correlation matrices

713
00:38:05,200 --> 00:38:07,119
so they defined uh the quotient of the

714
00:38:07,119 --> 00:38:09,040
assigning variant metric that i called

715
00:38:09,040 --> 00:38:11,599
the quotient defined metric

716
00:38:11,599 --> 00:38:15,520
and i derived all the the formulae that

717
00:38:15,520 --> 00:38:18,240
were not derived in the original work

718
00:38:18,240 --> 00:38:19,040
um such as the remaining metric that you

719
00:38:19,040 --> 00:38:20,640
can

720
00:38:20,640 --> 00:38:23,920
read here

721
00:38:23,920 --> 00:38:26,320
and so from this work uh we can

722
00:38:26,320 --> 00:38:29,440
i could

723
00:38:29,440 --> 00:38:32,560
derive some properties of this metric

724
00:38:32,560 --> 00:38:34,400
so it's a quotient romanian metric it is

725
00:38:34,400 --> 00:38:36,640
geologically complete because the

726
00:38:36,640 --> 00:38:37,839
defining variant metric is geodesically

727
00:38:37,839 --> 00:38:40,400
complete

728
00:38:40,400 --> 00:38:44,079
on spd matrices

729
00:38:44,079 --> 00:38:46,640
it is invariant under permutations

730
00:38:46,640 --> 00:38:49,280
and it is also core inverse consistent

731
00:38:49,280 --> 00:38:51,760
uh the core inverse evolution

732
00:38:51,760 --> 00:38:54,960
is what we would like to define as an

733
00:38:54,960 --> 00:38:58,240
inversion on correlation matrices

734
00:38:58,240 --> 00:39:02,160
um so if we inverse a covariance matrix

735
00:39:02,160 --> 00:39:03,520
we get what we call um

736
00:39:03,520 --> 00:39:07,280
um

737
00:39:07,280 --> 00:39:07,280
sorry i'm missing the word um

738
00:39:10,320 --> 00:39:14,280
i i cannot hear

739
00:39:16,240 --> 00:39:20,160
no no the

740
00:39:20,160 --> 00:39:22,000
uh i'm sorry it will come back

741
00:39:22,000 --> 00:39:26,000
precision metrics thank you

742
00:39:26,000 --> 00:39:26,000
that's why that's why we have an advisor

743
00:39:27,599 --> 00:39:33,520
the precision matrix and so uh on

744
00:39:33,520 --> 00:39:35,599
on correlation matrices uh the the

745
00:39:35,599 --> 00:39:38,000
equivalent uh

746
00:39:38,000 --> 00:39:40,079
is the core inverse involution because

747
00:39:40,079 --> 00:39:42,320
the inverse of the correlation matrix is

748
00:39:42,320 --> 00:39:44,240
not the correlation matrix so we have to

749
00:39:44,240 --> 00:39:47,520
renormalize

750
00:39:47,520 --> 00:39:50,720
and this core inverse matrix contains

751
00:39:50,720 --> 00:39:55,119
the partial correlations up to a sign

752
00:39:55,119 --> 00:39:57,920
and these these partial correlations are

753
00:39:57,920 --> 00:40:00,240
very important in several fields uh such

754
00:40:00,240 --> 00:40:02,400
as stationary stocks processes of

755
00:40:02,400 --> 00:40:04,160
gaussian graphical networks and so i

756
00:40:04,160 --> 00:40:05,440
think that this property of inverse

757
00:40:05,440 --> 00:40:08,400
consistency

758
00:40:08,400 --> 00:40:08,400
is very important

759
00:40:08,960 --> 00:40:14,400
so uh on this quotient defined metric i

760
00:40:14,400 --> 00:40:16,319
derived the formula of the geodesics and

761
00:40:16,319 --> 00:40:18,960
the curvature

762
00:40:18,960 --> 00:40:20,960
so you can see that the the the

763
00:40:20,960 --> 00:40:22,400
formula is quite ugly

764
00:40:22,400 --> 00:40:23,200
um

765
00:40:23,200 --> 00:40:24,800
and

766
00:40:24,800 --> 00:40:26,720
especially in the formula of the

767
00:40:26,720 --> 00:40:28,800
curvature there is a negative term and a

768
00:40:28,800 --> 00:40:31,050
positive term so it was a bit difficult

769
00:40:31,050 --> 00:40:33,119
to determine the sign of this

770
00:40:33,119 --> 00:40:34,240
[Music]

771
00:40:34,240 --> 00:40:36,400
curvature

772
00:40:36,400 --> 00:40:37,760
uh and so i proved that it's not of

773
00:40:37,760 --> 00:40:40,640
constant sign

774
00:40:40,640 --> 00:40:43,040
and that it is unbounded from above it

775
00:40:43,040 --> 00:40:46,400
means that we can find a sequence of

776
00:40:46,400 --> 00:40:48,720
correlation matrices such such that

777
00:40:48,720 --> 00:40:51,720
the curvature tends to infinite plus

778
00:40:51,720 --> 00:40:51,720
infinity

779
00:40:52,319 --> 00:40:55,760
and so uh

780
00:40:55,760 --> 00:40:58,960
this is not very

781
00:40:58,960 --> 00:41:00,880
convenient because we said that

782
00:41:00,880 --> 00:41:03,119
this space of full range correlation

783
00:41:03,119 --> 00:41:05,839
matrices is

784
00:41:05,839 --> 00:41:08,160
differmorphic to a vector space and so

785
00:41:08,160 --> 00:41:10,319
it's not convenient to have a curvature

786
00:41:10,319 --> 00:41:11,680
that is unbounded and not of constant

787
00:41:11,680 --> 00:41:14,160
sign

788
00:41:14,160 --> 00:41:17,440
so uh i would like to define

789
00:41:17,440 --> 00:41:20,160
differmorphisms uh towards vector spaces

790
00:41:20,160 --> 00:41:21,839
that preserve the good properties of the

791
00:41:21,839 --> 00:41:24,400
portion defined metric without the

792
00:41:24,400 --> 00:41:26,510
drawbacks

793
00:41:26,510 --> 00:41:28,000
so i proposed

794
00:41:28,000 --> 00:41:30,160
[Music]

795
00:41:30,160 --> 00:41:33,280
here three families

796
00:41:33,280 --> 00:41:35,760
of of such metrics

797
00:41:35,760 --> 00:41:39,359
the two first ones are adapted from

798
00:41:39,359 --> 00:41:42,319
works that were done on spd matrices

799
00:41:42,319 --> 00:41:42,319
um maybe we can

800
00:41:44,640 --> 00:41:49,680
so

801
00:41:49,680 --> 00:41:52,480
the the the adaptation is to renormalize

802
00:41:52,480 --> 00:41:53,760
by the the diagonal here

803
00:41:53,760 --> 00:41:54,800
and here

804
00:41:54,800 --> 00:41:57,520
um

805
00:41:57,520 --> 00:42:00,400
and so we get a deformation from full

806
00:42:00,400 --> 00:42:02,640
range correlation matrices to a space

807
00:42:02,640 --> 00:42:05,200
that we can utilize

808
00:42:05,200 --> 00:42:08,240
quite easily either by removing the

809
00:42:08,240 --> 00:42:10,480
identity or by taking the the metrics

810
00:42:10,480 --> 00:42:12,640
logarithm

811
00:42:12,640 --> 00:42:14,800
these two metrics are not invariant

812
00:42:14,800 --> 00:42:16,480
under permutations and they are not

813
00:42:16,480 --> 00:42:21,359
inverse consistent

814
00:42:21,359 --> 00:42:24,000
um so i wanted to find alternatives

815
00:42:24,000 --> 00:42:27,280
and so i found uh quite recently this

816
00:42:27,280 --> 00:42:29,359
this work from arshakov and

817
00:42:29,359 --> 00:42:32,079
um who defined

818
00:42:32,079 --> 00:42:35,680
um a bijection from full range

819
00:42:35,680 --> 00:42:38,560
correlation matrices to a vector space

820
00:42:38,560 --> 00:42:40,640
um and i proved that this

821
00:42:40,640 --> 00:42:42,640
log here

822
00:42:42,640 --> 00:42:46,319
is actually a diffumorphism and so since

823
00:42:46,319 --> 00:42:47,119
it is a morphism we can pull back

824
00:42:47,119 --> 00:42:49,839
the

825
00:42:49,839 --> 00:42:51,040
the inner products on hollow matrices

826
00:42:51,040 --> 00:42:52,400
symmetric

827
00:42:52,400 --> 00:42:55,680
uh without

828
00:42:55,680 --> 00:42:57,200
with new diagonal we can pull back

829
00:42:57,200 --> 00:42:59,520
the inner products

830
00:42:59,520 --> 00:43:02,319
and defined log euclidean metrics on

831
00:43:02,319 --> 00:43:04,480
full rank or election matrices

832
00:43:04,480 --> 00:43:06,880
their permutation invariant

833
00:43:06,880 --> 00:43:08,720
but they are not inverse consistent

834
00:43:08,720 --> 00:43:11,520
because

835
00:43:11,520 --> 00:43:13,119
here we remove the diagonal after taking

836
00:43:13,119 --> 00:43:14,880
the logarithm

837
00:43:14,880 --> 00:43:17,520
and so this is not compatible this

838
00:43:17,520 --> 00:43:20,640
linear operation is not compatible

839
00:43:20,640 --> 00:43:24,000
with the congruent action of

840
00:43:24,000 --> 00:43:26,710
diagonal matrices on spd matrices

841
00:43:26,710 --> 00:43:28,560
so what could be an alternative um

842
00:43:28,560 --> 00:43:31,520
[Music]

843
00:43:31,520 --> 00:43:34,319
we shouldn't take the logarithm because

844
00:43:34,319 --> 00:43:36,800
here we are quite obliged to

845
00:43:36,800 --> 00:43:39,520
remove the diagonal so i tried to find

846
00:43:39,520 --> 00:43:42,240
another path

847
00:43:42,240 --> 00:43:44,640
and i realized when i studied the

848
00:43:44,640 --> 00:43:46,560
permutation in variant inner products on

849
00:43:46,560 --> 00:43:48,640
the vector space of symmetric matrices

850
00:43:48,640 --> 00:43:51,440
that we can decompose the space of

851
00:43:51,440 --> 00:43:54,480
symmetric matrices

852
00:43:54,480 --> 00:43:56,960
with another space that is this row zero

853
00:43:56,960 --> 00:43:59,200
the space of symmetric matrices with new

854
00:43:59,200 --> 00:44:00,880
rows sums

855
00:44:00,880 --> 00:44:02,800
so this is

856
00:44:02,800 --> 00:44:05,760
a direct sum

857
00:44:05,760 --> 00:44:08,640
and then the exponential of this space

858
00:44:08,640 --> 00:44:11,760
is exactly the the manifold of spd

859
00:44:11,760 --> 00:44:14,000
matrices with the unit row sums

860
00:44:14,000 --> 00:44:16,560
and so we have three spaces that are in

861
00:44:16,560 --> 00:44:18,880
bijection with this

862
00:44:18,880 --> 00:44:21,359
matrix logarithm and so the last

863
00:44:21,359 --> 00:44:24,720
question is can we decompose spd

864
00:44:24,720 --> 00:44:27,520
matrices as a positive diagonal matrix

865
00:44:27,520 --> 00:44:29,839
and a matrix in row one plus

866
00:44:29,839 --> 00:44:32,960
the answer is yes it was given by

867
00:44:32,960 --> 00:44:34,720
marshall and alkene in 68 it's called

868
00:44:34,720 --> 00:44:38,079
the scaling

869
00:44:38,079 --> 00:44:39,839
so for all spd matrices there exists a

870
00:44:39,839 --> 00:44:42,720
unique

871
00:44:42,720 --> 00:44:47,040
positive diagonal matrix

872
00:44:47,040 --> 00:44:48,880
such that the congruence up to d

873
00:44:48,880 --> 00:44:51,760
of this

874
00:44:51,760 --> 00:44:54,079
spd matrix is in a row one plus and so

875
00:44:54,079 --> 00:44:56,880
we have this decomposition of spd

876
00:44:56,880 --> 00:45:00,800
matrices so now we can go from

877
00:45:00,800 --> 00:45:03,760
correlation matrices to row one plus

878
00:45:03,760 --> 00:45:06,640
by simply taking the scaling

879
00:45:06,640 --> 00:45:08,880
and we get a different morphism from the

880
00:45:08,880 --> 00:45:12,160
full range correlation matrices

881
00:45:12,160 --> 00:45:13,920
to this vector space rho zero

882
00:45:13,920 --> 00:45:15,119
so i proved that this is a different

883
00:45:15,119 --> 00:45:17,520
morphism

884
00:45:17,520 --> 00:45:19,920
it is permutation invariant

885
00:45:19,920 --> 00:45:20,880
and it is core inverse consistent

886
00:45:20,880 --> 00:45:21,839
because

887
00:45:21,839 --> 00:45:24,640
the

888
00:45:24,640 --> 00:45:26,960
the space rho 1 plus is stable by

889
00:45:26,960 --> 00:45:28,960
inversion

890
00:45:28,960 --> 00:45:31,280
and so the inversion corresponds exactly

891
00:45:31,280 --> 00:45:33,200
to the core inversion on on correlation

892
00:45:33,200 --> 00:45:36,960
matrices

893
00:45:36,960 --> 00:45:37,839
and so we preserved this this property

894
00:45:37,839 --> 00:45:39,520
from

895
00:45:39,520 --> 00:45:41,359
from the portion to find matrix so we

896
00:45:41,359 --> 00:45:43,839
have the good properties without the

897
00:45:43,839 --> 00:45:43,839
drawbacks

898
00:45:45,359 --> 00:45:50,319
so to summarize i computed the geometric

899
00:45:50,319 --> 00:45:53,359
operations of the quotient to find

900
00:45:53,359 --> 00:45:55,119
metric i defined four families of log

901
00:45:55,119 --> 00:45:57,359
euclidean metrics on full rank

902
00:45:57,359 --> 00:46:00,079
correlation matrices with interesting

903
00:46:00,079 --> 00:46:02,319
invariance properties

904
00:46:02,319 --> 00:46:04,400
and so this provides simple principled

905
00:46:04,400 --> 00:46:06,960
metrics to compute with full rank

906
00:46:06,960 --> 00:46:06,960
correlation

907
00:46:07,040 --> 00:46:13,240
and so these works also gave rise to

908
00:46:13,240 --> 00:46:13,240
several publications

909
00:46:14,000 --> 00:46:18,480
so now

910
00:46:18,480 --> 00:46:20,640
i will go to the third part um the blue

911
00:46:20,640 --> 00:46:22,400
association orbit space of covariance

912
00:46:22,400 --> 00:46:25,680
matrices

913
00:46:25,680 --> 00:46:28,560
so i will present briefly the the works

914
00:46:28,560 --> 00:46:30,720
the works on geodesics in each stratum

915
00:46:30,720 --> 00:46:32,480
and then i will explain you what are the

916
00:46:32,480 --> 00:46:36,319
geodesics

917
00:46:36,319 --> 00:46:36,319
in the in the total orbit space

918
00:46:37,920 --> 00:46:41,760
so in contrast with log euclidean

919
00:46:41,760 --> 00:46:45,200
metrics

920
00:46:45,200 --> 00:46:49,839
these metrics are not complete

921
00:46:49,839 --> 00:46:49,839
the geodesic is not unique between

922
00:46:50,800 --> 00:46:53,280
two points

923
00:46:53,280 --> 00:46:54,400
a theory

924
00:46:54,400 --> 00:46:56,640
and

925
00:46:56,640 --> 00:46:59,119
the geodesic needs not be globally

926
00:46:59,119 --> 00:47:02,079
amazing and so we need other notions to

927
00:47:02,079 --> 00:47:04,319
characterize the minimizing geodesics

928
00:47:04,319 --> 00:47:06,560
such as the definition interval

929
00:47:06,560 --> 00:47:10,319
the the set of pre-images

930
00:47:10,319 --> 00:47:12,480
and the cut time

931
00:47:12,480 --> 00:47:14,160
uh that allow so if we want to

932
00:47:14,160 --> 00:47:15,760
characterize the immunizing geodesics we

933
00:47:15,760 --> 00:47:17,040
need to characterize

934
00:47:17,040 --> 00:47:19,520
these

935
00:47:19,520 --> 00:47:19,520
notions

936
00:47:19,760 --> 00:47:24,800
so here is a summary of uh contributions

937
00:47:24,800 --> 00:47:27,359
of many

938
00:47:27,359 --> 00:47:29,680
researchers on this uh

939
00:47:29,680 --> 00:47:32,400
field

940
00:47:32,400 --> 00:47:34,240
on the bureaucracy metric

941
00:47:34,240 --> 00:47:36,240
um

942
00:47:36,240 --> 00:47:38,800
so

943
00:47:38,800 --> 00:47:42,480
my my contributions were especially to

944
00:47:42,480 --> 00:47:45,520
compute the cup time uh in both in in

945
00:47:45,520 --> 00:47:46,319
the main stratum of spd matrices and in

946
00:47:46,319 --> 00:47:48,880
other

947
00:47:48,880 --> 00:47:48,880
strata

948
00:47:49,119 --> 00:47:52,800
and it came from a problem that we had

949
00:47:52,800 --> 00:47:55,520
in in geostats

950
00:47:55,520 --> 00:47:57,200
when we were computing the the logarithm

951
00:47:57,200 --> 00:47:59,359
of the exponential the remaining

952
00:47:59,359 --> 00:48:01,680
logarithm of the remaining exponential

953
00:48:01,680 --> 00:48:04,400
we were not retrieving the

954
00:48:04,400 --> 00:48:06,240
initial tangent vector and it was coming

955
00:48:06,240 --> 00:48:08,559
from the fact that we were shooting too

956
00:48:08,559 --> 00:48:11,599
far so now we know the

957
00:48:11,599 --> 00:48:15,760
cut time and we know

958
00:48:15,760 --> 00:48:15,760
what we are allowed to do actually

959
00:48:16,960 --> 00:48:22,960
so now i will briefly present the

960
00:48:22,960 --> 00:48:25,040
concept of romanian orbit spaces uh to

961
00:48:25,040 --> 00:48:27,520
explain the

962
00:48:27,520 --> 00:48:29,040
the example of the the research time

963
00:48:29,040 --> 00:48:32,319
of its space

964
00:48:32,319 --> 00:48:34,960
so as defined in uh by alexevsky and

965
00:48:34,960 --> 00:48:36,720
colleagues a remaining orbit space is a

966
00:48:36,720 --> 00:48:38,800
remaining manifold

967
00:48:38,800 --> 00:48:41,280
on

968
00:48:41,280 --> 00:48:43,280
it is based on a remaining manifold on

969
00:48:43,280 --> 00:48:46,240
which we have a group action that is

970
00:48:46,240 --> 00:48:48,960
smooth proper and isometric

971
00:48:48,960 --> 00:48:49,920
and we are taking the quotient of this

972
00:48:49,920 --> 00:48:52,720
space

973
00:48:52,720 --> 00:48:56,079
so we can compute the orbits

974
00:48:56,079 --> 00:48:59,119
and we can show that uh the the orbits

975
00:48:59,119 --> 00:49:01,520
are exactly the covariance that we see

976
00:49:01,520 --> 00:49:04,480
and so the orbit space is the space of

977
00:49:04,480 --> 00:49:07,200
covariance matrices

978
00:49:07,200 --> 00:49:10,800
then we can compute the isotropic group

979
00:49:10,800 --> 00:49:10,800
also called the stabilizer

980
00:49:10,880 --> 00:49:14,319
and

981
00:49:14,319 --> 00:49:17,440
we can prove that

982
00:49:17,440 --> 00:49:21,040
two points have conjugate stabilizers if

983
00:49:21,040 --> 00:49:23,280
and only if they have the same rank

984
00:49:23,280 --> 00:49:25,599
and so we will retrieve

985
00:49:25,599 --> 00:49:27,359
the stratification by the rank of

986
00:49:27,359 --> 00:49:31,119
covariance matrices

987
00:49:31,119 --> 00:49:32,720
so first off of square matrices

988
00:49:32,720 --> 00:49:36,079
and then

989
00:49:36,079 --> 00:49:36,079
of covariance matrices

990
00:49:36,240 --> 00:49:41,119
so the strata

991
00:49:41,119 --> 00:49:45,720
the stratum of a matrix sigma is the set

992
00:49:45,720 --> 00:49:45,720
of all matrices that have the same range

993
00:49:47,280 --> 00:49:51,119
and so now we can

994
00:49:51,119 --> 00:49:53,839
characterize all the minimizing

995
00:49:53,839 --> 00:49:57,599
geodesics between any two covariance

996
00:49:57,599 --> 00:50:00,720
matrices independently from their ranks

997
00:50:00,720 --> 00:50:03,119
so the first property of orbit spaces

998
00:50:03,119 --> 00:50:04,319
is that on a minimizing judiciary cannot

999
00:50:04,319 --> 00:50:06,240
meet

1000
00:50:06,240 --> 00:50:08,800
more singular orbit

1001
00:50:08,800 --> 00:50:09,680
and so the direct consequence of this

1002
00:50:09,680 --> 00:50:11,040
fact

1003
00:50:11,040 --> 00:50:13,520
is that

1004
00:50:13,520 --> 00:50:17,760
any minimizing geodesic is of constant

1005
00:50:17,760 --> 00:50:17,760
brain on the interior of the segment

1006
00:50:18,960 --> 00:50:24,319
and thanks to the structure of the

1007
00:50:24,319 --> 00:50:28,880
remaining orbit space we can prove that

1008
00:50:28,880 --> 00:50:31,119
there are parameterized by this matrix r

1009
00:50:31,119 --> 00:50:33,440
that is an orthogonal matrix satisfying

1010
00:50:33,440 --> 00:50:36,559
this condition is transposed by r

1011
00:50:36,559 --> 00:50:39,359
transpose is appropriately is

1012
00:50:39,359 --> 00:50:41,839
positive semi-definite and symmetric

1013
00:50:41,839 --> 00:50:43,040
where x and y

1014
00:50:43,040 --> 00:50:44,800
are

1015
00:50:44,800 --> 00:50:47,040
defined like this

1016
00:50:47,040 --> 00:50:49,440
such that x x transpose is the first

1017
00:50:49,440 --> 00:50:51,839
point and y y transpose is the second

1018
00:50:51,839 --> 00:50:51,839
point

1019
00:50:52,480 --> 00:50:56,079
the problem is that this

1020
00:50:56,079 --> 00:50:59,839
parameterization is not injected um so

1021
00:50:59,839 --> 00:51:02,160
we cannot count the geodesics based on

1022
00:51:02,160 --> 00:51:06,160
this parameterization so i wanted to

1023
00:51:06,160 --> 00:51:06,160
find an injective parameterization

1024
00:51:06,960 --> 00:51:12,400
and so if we take uh

1025
00:51:12,400 --> 00:51:16,000
the same notations sigma 1 sigma 2 and x

1026
00:51:16,000 --> 00:51:18,079
y and we denote k l are

1027
00:51:18,079 --> 00:51:20,800
their ranks

1028
00:51:20,800 --> 00:51:24,960
without loss of generality we can assume

1029
00:51:24,960 --> 00:51:27,839
that the n minus k columns of x and the

1030
00:51:27,839 --> 00:51:29,119
n minus l columns of y

1031
00:51:29,119 --> 00:51:31,359
are new

1032
00:51:31,359 --> 00:51:34,839
because of the

1033
00:51:34,839 --> 00:51:37,680
the right action of the

1034
00:51:37,680 --> 00:51:38,640
orthogonal and thanks to this action

1035
00:51:38,640 --> 00:51:41,280
again

1036
00:51:41,280 --> 00:51:44,640
we can also without without loss of

1037
00:51:44,640 --> 00:51:45,839
generality assume that the product x

1038
00:51:45,839 --> 00:51:46,720
transpose

1039
00:51:46,720 --> 00:51:48,720
y

1040
00:51:48,720 --> 00:51:51,599
is a diagonal matrix

1041
00:51:51,599 --> 00:51:53,280
and thanks to these um

1042
00:51:53,280 --> 00:51:55,920
simplifications

1043
00:51:55,920 --> 00:51:57,680
uh we can now simply characterize the

1044
00:51:57,680 --> 00:52:00,559
matrix r

1045
00:52:00,559 --> 00:52:03,599
that we had on the previous slide

1046
00:52:03,599 --> 00:52:06,400
and so the matrix r has to be of this

1047
00:52:06,400 --> 00:52:07,760
form with an identity block

1048
00:52:07,760 --> 00:52:09,920
and

1049
00:52:09,920 --> 00:52:11,839
an orthogonal block here

1050
00:52:11,839 --> 00:52:14,960
and so this matrix

1051
00:52:14,960 --> 00:52:18,640
composed with r0 and r1

1052
00:52:18,640 --> 00:52:20,960
belongs to a stifle manifold

1053
00:52:20,960 --> 00:52:24,240
and

1054
00:52:24,240 --> 00:52:26,319
the the fact is that the the dimensions

1055
00:52:26,319 --> 00:52:27,599
are such that

1056
00:52:27,599 --> 00:52:30,559
um

1057
00:52:30,559 --> 00:52:34,000
the matrix r0 is unconstrained

1058
00:52:34,000 --> 00:52:37,040
uh in the in the closed unit bone

1059
00:52:37,040 --> 00:52:37,040
um for the

1060
00:52:37,920 --> 00:52:44,559
for the the spectrum normal

1061
00:52:44,559 --> 00:52:47,440
and so now uh if we compute the the

1062
00:52:47,440 --> 00:52:51,680
geodesics thanks to the previous formula

1063
00:52:51,680 --> 00:52:54,400
we can see that only r0 has an impact

1064
00:52:54,400 --> 00:52:57,920
on the geodesic so here is the new

1065
00:52:57,920 --> 00:53:00,400
formula parameterized by r0 in the

1066
00:53:00,400 --> 00:53:03,200
closed unit ball for the

1067
00:53:03,200 --> 00:53:03,200
spectral node

1068
00:53:04,000 --> 00:53:07,359
and now

1069
00:53:07,359 --> 00:53:10,160
this is an injective parametrization

1070
00:53:10,160 --> 00:53:12,480
with r0 appearing here

1071
00:53:12,480 --> 00:53:17,359
and so since it is injected we can now

1072
00:53:17,359 --> 00:53:17,359
count the number of minimizing geodesics

1073
00:53:17,680 --> 00:53:22,240
and so the geodesics the minimizing

1074
00:53:22,240 --> 00:53:24,400
geodesics uh

1075
00:53:24,400 --> 00:53:27,119
there is only there there exists a

1076
00:53:27,119 --> 00:53:30,640
unique minimizing geodesic if and only

1077
00:53:30,640 --> 00:53:33,839
if the rank of the product r

1078
00:53:33,839 --> 00:53:36,160
equals one of two ranks so here l is it

1079
00:53:36,160 --> 00:53:37,440
was supposed to be inferior to

1080
00:53:37,440 --> 00:53:40,160
k

1081
00:53:40,160 --> 00:53:42,559
in particular when k equals n

1082
00:53:42,559 --> 00:53:44,880
uh the geodesic is unique it means that

1083
00:53:44,880 --> 00:53:45,920
when we take two matrices

1084
00:53:45,920 --> 00:53:46,180
and

1085
00:53:46,180 --> 00:53:48,720
um

1086
00:53:48,720 --> 00:53:51,760
[Music]

1087
00:53:51,760 --> 00:53:54,319
such that one of the two is invertible

1088
00:53:54,319 --> 00:53:57,599
there is a unique minimizing geodesic

1089
00:53:57,599 --> 00:54:00,400
from an spd matrix to any other

1090
00:54:00,400 --> 00:54:01,520
covariance measure

1091
00:54:01,520 --> 00:54:04,559
otherwise

1092
00:54:04,559 --> 00:54:06,000
when r is not equal to l uh there is

1093
00:54:06,000 --> 00:54:08,240
infinitely many

1094
00:54:08,240 --> 00:54:11,720
minimizing geodesics between the two

1095
00:54:11,720 --> 00:54:11,720
covariance matrices

1096
00:54:11,760 --> 00:54:17,760
and there is an interesting choice for

1097
00:54:17,760 --> 00:54:20,160
r0 which corresponds to 0

1098
00:54:20,160 --> 00:54:21,680
and this choice leads to a canonical

1099
00:54:21,680 --> 00:54:22,839
expression

1100
00:54:22,839 --> 00:54:25,760
that

1101
00:54:25,760 --> 00:54:28,480
reduces to be the unique one when there

1102
00:54:28,480 --> 00:54:30,319
is a unique one

1103
00:54:30,319 --> 00:54:31,599
and so this formula

1104
00:54:31,599 --> 00:54:34,400
um

1105
00:54:34,400 --> 00:54:35,359
implies the the

1106
00:54:35,359 --> 00:54:36,960
the

1107
00:54:36,960 --> 00:54:38,960
the pseudo inversion

1108
00:54:38,960 --> 00:54:41,280
denoted dagger here

1109
00:54:41,280 --> 00:54:43,359
and so it's a close form formula between

1110
00:54:43,359 --> 00:54:46,160
of a geodesic minimizing geodesic

1111
00:54:46,160 --> 00:54:49,520
between two covariance matrices

1112
00:54:49,520 --> 00:54:50,559
that is independent from any choice of x

1113
00:54:50,559 --> 00:54:52,400
or y

1114
00:54:52,400 --> 00:54:55,200
and independent from

1115
00:54:55,200 --> 00:54:57,920
the ranks k l and r

1116
00:54:57,920 --> 00:55:00,960
uh mentioned previously

1117
00:55:00,960 --> 00:55:02,640
so to summarize i gave some compliments

1118
00:55:02,640 --> 00:55:04,720
on

1119
00:55:04,720 --> 00:55:06,640
the geodesics in each stratum of

1120
00:55:06,640 --> 00:55:07,920
covariance matrices

1121
00:55:07,920 --> 00:55:10,160
and i gave

1122
00:55:10,160 --> 00:55:12,720
the expression and the exact number of

1123
00:55:12,720 --> 00:55:15,680
minimizing geodesics in the orbit space

1124
00:55:15,680 --> 00:55:18,720
of covariance matrices

1125
00:55:18,720 --> 00:55:22,640
so this allows to interpolate between

1126
00:55:22,640 --> 00:55:23,440
covariance matrices of different ranks

1127
00:55:23,440 --> 00:55:26,079
um

1128
00:55:26,079 --> 00:55:28,400
and this gives a pedagogical example of

1129
00:55:28,400 --> 00:55:30,960
geodesics in a remaining orbit space and

1130
00:55:30,960 --> 00:55:33,599
so i hope this will allow to compute

1131
00:55:33,599 --> 00:55:34,400
other minimizing minimizing geodesics in

1132
00:55:34,400 --> 00:55:36,720
other

1133
00:55:36,720 --> 00:55:39,280
spaces such as the space of correlation

1134
00:55:39,280 --> 00:55:39,280
matrices

1135
00:55:39,520 --> 00:55:46,160
and so this work gave rise to this

1136
00:55:46,160 --> 00:55:46,160
submission in cymax

1137
00:55:46,880 --> 00:55:53,599
so to conclude um

1138
00:55:53,599 --> 00:55:55,760
on the manifold of spd matrices i gave

1139
00:55:55,760 --> 00:55:57,680
some compliments on the curvature of the

1140
00:55:57,680 --> 00:55:59,359
fine invariant metric and the parallel

1141
00:55:59,359 --> 00:56:01,520
transport of your software network that

1142
00:56:01,520 --> 00:56:04,319
i didn't present here but that are

1143
00:56:04,319 --> 00:56:06,960
detailed in the manuscript

1144
00:56:06,960 --> 00:56:10,000
i propose the elements of classification

1145
00:56:10,000 --> 00:56:11,760
uh of riemann matrix and i characterized

1146
00:56:11,760 --> 00:56:14,559
the continuous

1147
00:56:14,559 --> 00:56:16,480
o of n invariant matrix

1148
00:56:16,480 --> 00:56:18,480
and i defined this family of mixed

1149
00:56:18,480 --> 00:56:20,799
eukidian matrix

1150
00:56:20,799 --> 00:56:23,839
that i related to the alpha beta

1151
00:56:23,839 --> 00:56:26,720
divergences and for which i compute i i

1152
00:56:26,720 --> 00:56:29,440
derived the curvature

1153
00:56:29,440 --> 00:56:32,319
on following correlation matrices

1154
00:56:32,319 --> 00:56:34,799
i computed the geometric operations of

1155
00:56:34,799 --> 00:56:36,960
the quotient defined metric

1156
00:56:36,960 --> 00:56:39,599
and i proved that the curvature is

1157
00:56:39,599 --> 00:56:42,559
unbounded and this needed to

1158
00:56:42,559 --> 00:56:42,559
introduce other

1159
00:56:42,880 --> 00:56:47,599
other metrics that are log european and

1160
00:56:47,599 --> 00:56:50,079
especially a family of permutation

1161
00:56:50,079 --> 00:56:53,200
invariant for inverse consistent

1162
00:56:53,200 --> 00:56:53,200
logarithmic matrix

1163
00:56:53,760 --> 00:56:58,000
and on the orbit space of coherence

1164
00:56:58,000 --> 00:57:00,559
matrices on the stratified space of

1165
00:57:00,559 --> 00:57:02,319
covariance matrices i

1166
00:57:02,319 --> 00:57:04,880
took this

1167
00:57:04,880 --> 00:57:07,760
the oversearch time distance and i gave

1168
00:57:07,760 --> 00:57:09,200
some complements image to stratum and i

1169
00:57:09,200 --> 00:57:12,400
characterized

1170
00:57:12,400 --> 00:57:15,359
the minimizing geodesics

1171
00:57:15,359 --> 00:57:15,359
of this distance

1172
00:57:15,839 --> 00:57:22,000
so this gave rise to several uh

1173
00:57:22,000 --> 00:57:26,680
publications or submissions

1174
00:57:26,680 --> 00:57:26,680
in conferences in journals

1175
00:57:27,200 --> 00:57:32,960
and so i would like to give some

1176
00:57:32,960 --> 00:57:33,839
perspectives uh to this work um

1177
00:57:33,839 --> 00:57:35,200
so

1178
00:57:35,200 --> 00:57:37,839
i think that

1179
00:57:37,839 --> 00:57:39,200
it would be worth to apply all this to

1180
00:57:39,200 --> 00:57:42,319
several

1181
00:57:42,319 --> 00:57:46,000
research domains such as genetics

1182
00:57:46,000 --> 00:57:48,400
electrons photography and many others

1183
00:57:48,400 --> 00:57:50,799
and i think that the these product

1184
00:57:50,799 --> 00:57:53,280
metrics on covariance matrices are

1185
00:57:53,280 --> 00:57:55,280
composed of a

1186
00:57:55,280 --> 00:57:56,640
part on the scaling and the part on the

1187
00:57:56,640 --> 00:57:58,559
correlation

1188
00:57:58,559 --> 00:58:01,520
uh are quite promising because they are

1189
00:58:01,520 --> 00:58:04,079
completely different than all the

1190
00:58:04,079 --> 00:58:07,200
the the metrics that were used on on

1191
00:58:07,200 --> 00:58:09,440
covariance matrices we proved that

1192
00:58:09,440 --> 00:58:11,520
the the correlation the correlation

1193
00:58:11,520 --> 00:58:14,480
coefficient is interpolated

1194
00:58:14,480 --> 00:58:16,079
monotonically with this kind of matrix

1195
00:58:16,079 --> 00:58:19,200
which is not the case with all the

1196
00:58:19,200 --> 00:58:21,280
metrics that were used until now

1197
00:58:21,280 --> 00:58:23,440
so if the correlation is important in

1198
00:58:23,440 --> 00:58:25,760
the application it should give it should

1199
00:58:25,760 --> 00:58:28,720
give interesting results

1200
00:58:28,720 --> 00:58:31,520
i would also like to uh transpose the

1201
00:58:31,520 --> 00:58:33,359
work i did uh on covariance matrices the

1202
00:58:33,359 --> 00:58:35,440
on the viewer sustain orbit space of

1203
00:58:35,440 --> 00:58:36,400
covariance matrices to correlation

1204
00:58:36,400 --> 00:58:38,960
matrices

1205
00:58:38,960 --> 00:58:41,440
the space was already described the the

1206
00:58:41,440 --> 00:58:44,960
geometry of the of the space but not the

1207
00:58:44,960 --> 00:58:46,240
geometry of the distance that we can put

1208
00:58:46,240 --> 00:58:49,359
on it

1209
00:58:49,359 --> 00:58:52,000
so i think it would be interesting to

1210
00:58:52,000 --> 00:58:56,799
uh to compute to derive all the all the

1211
00:58:56,799 --> 00:58:56,799
formulae or to compute them numerically

1212
00:58:57,760 --> 00:59:00,799
um

1213
00:59:00,799 --> 00:59:03,599
i think that

1214
00:59:03,599 --> 00:59:06,799
we can also relate some other works

1215
00:59:06,799 --> 00:59:09,119
to these works especially the works on

1216
00:59:09,119 --> 00:59:10,040
spaces of matrices of any size and any

1217
00:59:10,040 --> 00:59:11,760
rank

1218
00:59:11,760 --> 00:59:12,960
[Music]

1219
00:59:12,960 --> 00:59:15,359
because

1220
00:59:15,359 --> 00:59:17,440
it was only done

1221
00:59:17,440 --> 00:59:19,760
from what i know on grass minions and

1222
00:59:19,760 --> 00:59:23,440
spd matrices and i think it would be

1223
00:59:23,440 --> 00:59:24,319
transposed to psd matrices to

1224
00:59:24,319 --> 00:59:26,480
semi

1225
00:59:26,480 --> 00:59:29,440
positive definite

1226
00:59:29,440 --> 00:59:30,880
positive semi-definite matrices

1227
00:59:30,880 --> 00:59:33,920
because we can

1228
00:59:33,920 --> 00:59:35,920
a psd matrix is represented by a

1229
00:59:35,920 --> 00:59:37,680
cylinder or a

1230
00:59:37,680 --> 00:59:40,240
ring deficient matrix

1231
00:59:40,240 --> 00:59:42,720
and so i think it could be generalized

1232
00:59:42,720 --> 00:59:45,680
and i also worked on

1233
00:59:45,680 --> 00:59:47,280
permutation invariant and

1234
00:59:47,280 --> 00:59:49,040
block orthogonal invariant in your

1235
00:59:49,040 --> 00:59:51,119
products and i think

1236
00:59:51,119 --> 00:59:53,119
uh this could relate to the

1237
00:59:53,119 --> 00:59:54,640
stratification by eigenvalue

1238
00:59:54,640 --> 00:59:57,680
multiplicity

1239
00:59:57,680 --> 00:59:58,480
and uh the the works on on flags so it

1240
00:59:58,480 --> 01:00:01,839
should

1241
01:00:01,839 --> 01:00:01,839
be interesting to work on these

1242
01:00:02,079 --> 01:00:06,480
uh i would like to sincerely thank my

1243
01:00:06,480 --> 01:00:07,280
supervisor xavier

1244
01:00:07,280 --> 01:00:09,599
and

1245
01:00:09,599 --> 01:00:12,880
many other people that i will probably

1246
01:00:12,880 --> 01:00:14,720
have the opportunity to thank uh later

1247
01:00:14,720 --> 01:00:17,440
thank you for your attention and i'm

1248
01:00:17,440 --> 01:00:19,599
open to any questions and comments thank

1249
01:00:19,599 --> 01:00:19,599
you

1250
01:00:19,839 --> 01:00:25,520
thank you very much uh yen um

1251
01:00:25,520 --> 01:00:27,040
perfect timing and and i wish i had this

1252
01:00:27,040 --> 01:00:29,280
presentation before reading the

1253
01:00:29,280 --> 01:00:31,119
dissertation because it's you very

1254
01:00:31,119 --> 01:00:32,720
nicely highlighted the important

1255
01:00:32,720 --> 01:00:33,920
contributions

1256
01:00:33,920 --> 01:00:36,079
so it was

1257
01:00:36,079 --> 01:00:38,240
extremely clear in my opinion

1258
01:00:38,240 --> 01:00:39,760
so now um the

1259
01:00:39,760 --> 01:00:41,520
members of the

1260
01:00:41,520 --> 01:00:43,599
of the jewelry will have an opportunity

1261
01:00:43,599 --> 01:00:45,760
to ask a few questions

1262
01:00:45,760 --> 01:00:48,480
and we will start with

1263
01:00:48,480 --> 01:00:50,960
the two reporters and so the first one

1264
01:00:50,960 --> 01:00:54,079
being um pierre antoinepsil from

1265
01:00:54,079 --> 01:00:56,400
university of levant so please pierrot

1266
01:00:56,400 --> 01:00:57,520
thank you it was a great presentation

1267
01:00:57,520 --> 01:00:59,280
indeed

1268
01:00:59,280 --> 01:01:00,799
so the thesis is very deep many

1269
01:01:00,799 --> 01:01:03,040
contributions so i thought you would

1270
01:01:03,040 --> 01:01:04,640
have to focus on some aspects

1271
01:01:04,640 --> 01:01:06,799
but in fact you were able to give an

1272
01:01:06,799 --> 01:01:08,720
overview of the whole world that's

1273
01:01:08,720 --> 01:01:11,359
really impressive

1274
01:01:11,359 --> 01:01:13,280
i said many nice words about the thesis

1275
01:01:13,280 --> 01:01:15,440
in my report so i will

1276
01:01:15,440 --> 01:01:18,160
i really like it is an impressive work i

1277
01:01:18,160 --> 01:01:20,640
will go straight to a few questions

1278
01:01:20,640 --> 01:01:22,880
so we had some discussions about details

1279
01:01:22,880 --> 01:01:25,200
uh some time ago so my questions now are

1280
01:01:25,200 --> 01:01:27,280
more high level

1281
01:01:27,280 --> 01:01:28,960
uh but okay so when you reach the the

1282
01:01:28,960 --> 01:01:31,119
green part of your presentation so the

1283
01:01:31,119 --> 01:01:32,400
part part two about the correlation

1284
01:01:32,400 --> 01:01:34,559
matrices

1285
01:01:34,559 --> 01:01:36,240
excuse me i i just interrupt you i'm

1286
01:01:36,240 --> 01:01:38,480
sorry uh

1287
01:01:38,480 --> 01:01:41,040
i i'm just trying to see your face on

1288
01:01:41,040 --> 01:01:44,960
the screen because i'm not able to

1289
01:01:44,960 --> 01:01:46,799
to see you so uh okay so i see myself

1290
01:01:46,799 --> 01:01:48,880
okay

1291
01:01:48,880 --> 01:01:50,400
i'm just trying to see you because

1292
01:01:50,400 --> 01:01:53,040
otherwise it's more difficult to

1293
01:01:53,040 --> 01:01:53,040
understand

1294
01:01:56,160 --> 01:02:01,680
maybe you could stop sharing yeah i

1295
01:02:01,680 --> 01:02:05,200
stopped it but then uh i think

1296
01:02:05,200 --> 01:02:08,400
well i thought when i i would try again

1297
01:02:08,400 --> 01:02:12,160
your faces will reappear

1298
01:02:12,160 --> 01:02:15,710
but actually okay

1299
01:02:15,710 --> 01:02:15,710
[Music]

1300
01:02:24,559 --> 01:02:28,720
i don't think we need the slide for the

1301
01:02:28,720 --> 01:02:31,039
discussion so i think you can just stop

1302
01:02:31,039 --> 01:02:32,640
sharing okay that would be okay i was

1303
01:02:32,640 --> 01:02:34,960
going to ask some scenes that we didn't

1304
01:02:34,960 --> 01:02:34,960
slide

1305
01:02:40,799 --> 01:02:45,119
okay sorry so now i can see you

1306
01:02:45,119 --> 01:02:48,000
okay

1307
01:02:48,000 --> 01:02:50,240
so you when you start discussing these

1308
01:02:50,240 --> 01:02:52,000
full run correlation matrices

1309
01:02:52,000 --> 01:02:55,680
right away you want to go for the

1310
01:02:55,680 --> 01:02:58,000
quotient of the spd manifold quotient by

1311
01:02:58,000 --> 01:03:00,000
the congruent section of the positive

1312
01:03:00,000 --> 01:03:02,000
diagonal matrices

1313
01:03:02,000 --> 01:03:04,240
but maybe another approach would be to

1314
01:03:04,240 --> 01:03:07,039
just consider the set of foreign

1315
01:03:07,039 --> 01:03:10,480
correlation matrices as a sub manifold

1316
01:03:10,480 --> 01:03:12,559
of the spd manifold

1317
01:03:12,559 --> 01:03:15,440
and then whenever you have a metric on

1318
01:03:15,440 --> 01:03:17,520
the spd manifold you induce a metric on

1319
01:03:17,520 --> 01:03:19,039
the sub manifold

1320
01:03:19,039 --> 01:03:21,200
so what would you think about that

1321
01:03:21,200 --> 01:03:23,680
approach would it be too hard to say

1322
01:03:23,680 --> 01:03:27,359
anything clever about

1323
01:03:27,359 --> 01:03:30,400
these geometries of declaration matrices

1324
01:03:30,400 --> 01:03:33,200
or would you lose some key properties or

1325
01:03:33,200 --> 01:03:34,319
whatever what do you think about that id

1326
01:03:34,319 --> 01:03:36,960
um

1327
01:03:36,960 --> 01:03:37,920
so yeah it's true that whenever we have

1328
01:03:37,920 --> 01:03:40,720
a

1329
01:03:40,720 --> 01:03:42,799
remaining metric on the manifold we can

1330
01:03:42,799 --> 01:03:45,760
look at the remaining metric

1331
01:03:45,760 --> 01:03:46,799
on a sub-manifold the induced remaining

1332
01:03:46,799 --> 01:03:49,920
metric

1333
01:03:49,920 --> 01:03:52,960
and so it was a bit

1334
01:03:52,960 --> 01:03:56,000
studied especially if we take the fine

1335
01:03:56,000 --> 01:03:56,000
invariant metric that

1336
01:03:57,839 --> 01:04:03,200
also called the the fischer raul metric

1337
01:04:03,200 --> 01:04:06,480
nibsen uh looked at it

1338
01:04:06,480 --> 01:04:08,160
and they proved that uh the

1339
01:04:08,160 --> 01:04:11,520
the manifold of urant correlation

1340
01:04:11,520 --> 01:04:12,799
matrices is not uh totally geodesic have

1341
01:04:12,799 --> 01:04:14,720
manifold

1342
01:04:14,720 --> 01:04:18,880
for this metric

1343
01:04:18,880 --> 01:04:21,760
and i think that um

1344
01:04:21,760 --> 01:04:24,319
we can easily prove i think i i wrote it

1345
01:04:24,319 --> 01:04:26,559
in the manuscript but i didn't properly

1346
01:04:26,559 --> 01:04:28,319
prove it but i think it's it shouldn't

1347
01:04:28,319 --> 01:04:30,880
be

1348
01:04:30,880 --> 01:04:32,960
too difficult to prove that

1349
01:04:32,960 --> 01:04:34,720
it's also true for the log euclidean and

1350
01:04:34,720 --> 01:04:36,799
the vascular time metric

1351
01:04:36,799 --> 01:04:39,119
that the the set of following

1352
01:04:39,119 --> 01:04:42,799
correlation matrices is not

1353
01:04:42,799 --> 01:04:42,799
totally geodesic subspace

1354
01:04:42,960 --> 01:04:46,880
the the problem with these

1355
01:04:46,880 --> 01:04:49,680
[Music]

1356
01:04:49,680 --> 01:04:53,039
this induced remaining metric is that we

1357
01:04:53,039 --> 01:04:54,559
cannot say many things uh

1358
01:04:54,559 --> 01:04:57,839
on on

1359
01:04:57,839 --> 01:05:00,480
the on the subspace uh we so for example

1360
01:05:00,480 --> 01:05:03,920
we can compute the the curvature uh i

1361
01:05:03,920 --> 01:05:05,280
think it's for me from from carton

1362
01:05:05,280 --> 01:05:08,559
um

1363
01:05:08,559 --> 01:05:10,240
carton and and some someone else let's

1364
01:05:10,240 --> 01:05:12,079
remember

1365
01:05:12,079 --> 01:05:14,559
that the equations i think

1366
01:05:14,559 --> 01:05:16,240
uh that so we can compute the the

1367
01:05:16,240 --> 01:05:17,280
curvature but for example we cannot

1368
01:05:17,280 --> 01:05:18,720
compute

1369
01:05:18,720 --> 01:05:20,319
um

1370
01:05:20,319 --> 01:05:22,880
we don't have

1371
01:05:22,880 --> 01:05:24,559
a generic way to derive the the

1372
01:05:24,559 --> 01:05:25,839
geodesics so we can compute them

1373
01:05:25,839 --> 01:05:29,119
numerically

1374
01:05:29,119 --> 01:05:31,359
and so of course i probably we we could

1375
01:05:31,359 --> 01:05:31,359
uh

1376
01:05:31,760 --> 01:05:36,319
we could do everything numerically uh

1377
01:05:36,319 --> 01:05:38,079
and actually in indium stats i think we

1378
01:05:38,079 --> 01:05:40,720
have all the tools to compute it

1379
01:05:40,720 --> 01:05:43,359
numerically um but

1380
01:05:43,359 --> 01:05:46,119
uh i like to have a close form for me

1381
01:05:46,119 --> 01:05:50,160
for everything and so every time we have

1382
01:05:50,160 --> 01:05:53,680
a sub romanian manifold and not separate

1383
01:05:53,680 --> 01:05:55,599
i i don't say subremanion

1384
01:05:55,599 --> 01:05:58,319
a same manifold with an induced romanian

1385
01:05:58,319 --> 01:06:00,079
metric um

1386
01:06:00,079 --> 01:06:02,559
we cannot compute many things that's why

1387
01:06:02,559 --> 01:06:04,480
i didn't really investigate this

1388
01:06:04,480 --> 01:06:06,880
direction

1389
01:06:06,880 --> 01:06:08,400
yeah okay yeah that certainly makes

1390
01:06:08,400 --> 01:06:11,760
sense

1391
01:06:11,760 --> 01:06:12,559
maybe another question is so now

1392
01:06:12,559 --> 01:06:14,480
uh

1393
01:06:14,480 --> 01:06:15,920
partly by your thought actually we have

1394
01:06:15,920 --> 01:06:17,760
a

1395
01:06:17,760 --> 01:06:20,480
big amount of possible geometries that

1396
01:06:20,480 --> 01:06:23,440
we can put on the spd manifold

1397
01:06:23,440 --> 01:06:25,680
and so if someone comes to that area a

1398
01:06:25,680 --> 01:06:27,520
user could be a bit puzzled

1399
01:06:27,520 --> 01:06:29,280
to have a choice between so many

1400
01:06:29,280 --> 01:06:30,880
possibilities

1401
01:06:30,880 --> 01:06:33,039
so i was wondering whether it's possible

1402
01:06:33,039 --> 01:06:35,839
to somehow fix that by doing something

1403
01:06:35,839 --> 01:06:38,079
like a decision tree that you could

1404
01:06:38,079 --> 01:06:40,000
offer to users

1405
01:06:40,000 --> 01:06:42,960
asking a few questions and then to guide

1406
01:06:42,960 --> 01:06:44,319
the users to the best geometry for for

1407
01:06:44,319 --> 01:06:45,680
their problem

1408
01:06:45,680 --> 01:06:47,280
do you think that would be possible to

1409
01:06:47,280 --> 01:06:48,960
do something like that do you have any

1410
01:06:48,960 --> 01:06:51,680
idea what the first question would be in

1411
01:06:51,680 --> 01:06:53,440
such a decision tree

1412
01:06:53,440 --> 01:06:54,880
ah questions yeah

1413
01:06:54,880 --> 01:06:58,240
so um

1414
01:06:58,240 --> 01:06:59,680
at first i think it's not my fault

1415
01:06:59,680 --> 01:07:02,400
i think you're thought

1416
01:07:02,400 --> 01:07:04,160
but maybe partially but uh

1417
01:07:04,160 --> 01:07:07,119
actually i tried to

1418
01:07:07,119 --> 01:07:09,760
to give some some principles to classify

1419
01:07:09,760 --> 01:07:11,359
them and to to to understand especially

1420
01:07:11,359 --> 01:07:13,119
what are the differences between them

1421
01:07:13,119 --> 01:07:15,520
but they were quite

1422
01:07:15,520 --> 01:07:16,400
i think almost all were already there

1423
01:07:16,400 --> 01:07:19,599
um

1424
01:07:19,599 --> 01:07:21,039
and so uh so i i think these principles

1425
01:07:21,039 --> 01:07:23,520
so maybe

1426
01:07:23,520 --> 01:07:26,880
i didn't want to to present it on the

1427
01:07:26,880 --> 01:07:28,640
slide because it was too small

1428
01:07:28,640 --> 01:07:31,119
but on

1429
01:07:31,119 --> 01:07:32,160
on page 25 in the in the manuscript

1430
01:07:32,160 --> 01:07:33,200
there is a

1431
01:07:33,200 --> 01:07:35,760
tree

1432
01:07:35,760 --> 01:07:37,839
or i it's not exactly a tree but it's a

1433
01:07:37,839 --> 01:07:41,039
graph

1434
01:07:41,039 --> 01:07:42,480
and i think that the the questions of

1435
01:07:42,480 --> 01:07:43,920
invariance

1436
01:07:43,920 --> 01:07:45,039
um

1437
01:07:45,039 --> 01:07:47,280
are

1438
01:07:47,280 --> 01:07:47,280
i mean

1439
01:07:47,599 --> 01:07:51,520
you you when you say questions you mean

1440
01:07:51,520 --> 01:07:55,039
uh

1441
01:07:55,039 --> 01:07:56,559
questions that the researcher could um

1442
01:07:56,559 --> 01:07:58,960
yeah i mean questions that would appear

1443
01:07:58,960 --> 01:08:00,960
in the decision tree so criteria

1444
01:08:00,960 --> 01:08:03,280
against which discriminate between the

1445
01:08:03,280 --> 01:08:05,359
choice of various metrics

1446
01:08:05,359 --> 01:08:07,359
so i think that all the

1447
01:08:07,359 --> 01:08:09,760
for example the principles of invariance

1448
01:08:09,760 --> 01:08:11,760
could help discriminate between uh

1449
01:08:11,760 --> 01:08:13,839
several metrics but it's true that for

1450
01:08:13,839 --> 01:08:15,039
example there are almost all uh

1451
01:08:15,039 --> 01:08:18,560
invariant and there are autobahn

1452
01:08:18,560 --> 01:08:20,480
transformations and so if you look for a

1453
01:08:20,480 --> 01:08:22,960
metric that is invariant and there is

1454
01:08:22,960 --> 01:08:23,759
this group action you are not very

1455
01:08:23,759 --> 01:08:24,719
uh

1456
01:08:24,719 --> 01:08:25,679
helps

1457
01:08:25,679 --> 01:08:28,319
um

1458
01:08:28,319 --> 01:08:29,440
so then

1459
01:08:29,440 --> 01:08:30,560
actually

1460
01:08:30,560 --> 01:08:32,960
in the

1461
01:08:32,960 --> 01:08:35,440
in the applications um

1462
01:08:35,440 --> 01:08:39,279
most of the time the it's the either

1463
01:08:39,279 --> 01:08:41,759
defining variant or the login metrical

1464
01:08:41,759 --> 01:08:42,799
that are used especially when we want to

1465
01:08:42,799 --> 01:08:45,199
reject

1466
01:08:45,199 --> 01:08:47,040
the the the

1467
01:08:47,040 --> 01:08:49,839
rank deficient matrices to infinity

1468
01:08:49,839 --> 01:08:51,199
that's another criterion actually if we

1469
01:08:51,199 --> 01:08:53,920
want the

1470
01:08:53,920 --> 01:08:55,600
the rank deficient matrices at finite or

1471
01:08:55,600 --> 01:08:57,040
infinite distance

1472
01:08:57,040 --> 01:09:00,640
um

1473
01:09:00,640 --> 01:09:03,040
and so and so in applications when the

1474
01:09:03,040 --> 01:09:04,560
when we want the the

1475
01:09:04,560 --> 01:09:05,679
rank deficient matrices at finite

1476
01:09:05,679 --> 01:09:08,640
distance

1477
01:09:08,640 --> 01:09:10,080
i saw that many people use the the

1478
01:09:10,080 --> 01:09:11,520
what i call the bureau of search time

1479
01:09:11,520 --> 01:09:14,000
distance or

1480
01:09:14,000 --> 01:09:15,679
but you called it maybe differently it's

1481
01:09:15,679 --> 01:09:17,520
also called proposed

1482
01:09:17,520 --> 01:09:18,560
um but

1483
01:09:18,560 --> 01:09:21,759
so

1484
01:09:21,759 --> 01:09:26,239
yeah and they they proved to to be very

1485
01:09:26,239 --> 01:09:26,239
uh efficient on the examples that were

1486
01:09:28,000 --> 01:09:31,279
investigated so

1487
01:09:31,359 --> 01:09:35,100
i

1488
01:09:35,100 --> 01:09:36,239
we tried actually to to

1489
01:09:36,239 --> 01:09:38,239
[Music]

1490
01:09:38,239 --> 01:09:40,799
for example

1491
01:09:40,799 --> 01:09:43,279
instead of a decision tree we could uh

1492
01:09:43,279 --> 01:09:45,359
when we have a family of romanian matrix

1493
01:09:45,359 --> 01:09:48,480
that is parameterized by for example two

1494
01:09:48,480 --> 01:09:50,400
parameters we could try to optimize the

1495
01:09:50,400 --> 01:09:53,920
metric uh

1496
01:09:53,920 --> 01:09:57,120
to fit it to some uh

1497
01:09:57,120 --> 01:09:59,679
training data and then test them

1498
01:09:59,679 --> 01:10:00,960
and actually um

1499
01:10:00,960 --> 01:10:04,159
we we were

1500
01:10:04,159 --> 01:10:05,760
retrieving kind of the the

1501
01:10:05,760 --> 01:10:07,360
metrics that are close to the log

1502
01:10:07,360 --> 01:10:09,679
equilibrium metric or the defining

1503
01:10:09,679 --> 01:10:11,760
variant metric or the

1504
01:10:11,760 --> 01:10:13,120
square root metric also

1505
01:10:13,120 --> 01:10:16,400
so

1506
01:10:16,400 --> 01:10:18,880
i'm not sure that

1507
01:10:18,880 --> 01:10:18,880
we could

1508
01:10:18,960 --> 01:10:23,600
we could find questions on in in a

1509
01:10:23,600 --> 01:10:25,040
decision tree but

1510
01:10:25,040 --> 01:10:27,760
i would say that

1511
01:10:27,760 --> 01:10:30,400
so far the the the best alternative

1512
01:10:30,400 --> 01:10:31,840
seems seem to be the defining variant

1513
01:10:31,840 --> 01:10:34,640
the log euclidean and the global

1514
01:10:34,640 --> 01:10:34,640
searching matrix

1515
01:10:35,199 --> 01:10:39,520
okay yeah thanks so indeed maybe there

1516
01:10:39,520 --> 01:10:41,520
is not a need for such a complicated

1517
01:10:41,520 --> 01:10:43,840
tree because maybe you are realizing

1518
01:10:43,840 --> 01:10:46,400
that in most applications it's one of

1519
01:10:46,400 --> 01:10:48,960
three principal metrics that seem to be

1520
01:10:48,960 --> 01:10:50,880
doing the best jobs yeah yeah and and

1521
01:10:50,880 --> 01:10:52,560
the main difference between

1522
01:10:52,560 --> 01:10:54,800
on on one side you're finding there and

1523
01:10:54,800 --> 01:10:56,880
log in region and on the other side the

1524
01:10:56,880 --> 01:11:00,320
the view of a software metric is the the

1525
01:11:00,320 --> 01:11:00,320
distance uh

1526
01:11:01,199 --> 01:11:04,719
the rank deficient matrices uh

1527
01:11:04,719 --> 01:11:07,280
but then

1528
01:11:07,280 --> 01:11:09,600
actually it's quite an open question to

1529
01:11:09,600 --> 01:11:12,239
to understand um

1530
01:11:12,239 --> 01:11:14,719
why define invariant metrics so it's in

1531
01:11:14,719 --> 01:11:16,640
variant under any uh a fine

1532
01:11:16,640 --> 01:11:18,719
reparameterization

1533
01:11:18,719 --> 01:11:20,159
of the the data set

1534
01:11:20,159 --> 01:11:22,080
and

1535
01:11:22,080 --> 01:11:25,360
it's quite an open question to

1536
01:11:25,360 --> 01:11:27,920
understand why it is the one that works

1537
01:11:27,920 --> 01:11:31,280
the best when we want to reject the the

1538
01:11:31,280 --> 01:11:33,120
rank definition matrices so

1539
01:11:33,120 --> 01:11:35,280
yeah it's it's wide open

1540
01:11:35,280 --> 01:11:37,120
yeah well it has a long list of like

1541
01:11:37,120 --> 01:11:38,719
nice properties as it's a angle in

1542
01:11:38,719 --> 01:11:42,560
matias properties

1543
01:11:42,560 --> 01:11:42,560
yep be part of the answer but yeah

1544
01:11:42,960 --> 01:11:49,280
uh mr chairman i have time for last

1545
01:11:49,280 --> 01:11:51,760
quick question yes quickly yeah okay uh

1546
01:11:51,760 --> 01:11:53,520
so in chapter 9 you have this very nice

1547
01:11:53,520 --> 01:11:56,159
application of the theory of a remaining

1548
01:11:56,159 --> 01:11:57,520
orbit spaces you can go very far in this

1549
01:11:57,520 --> 01:11:59,440
spd

1550
01:11:59,440 --> 01:12:01,120
this set of

1551
01:12:01,120 --> 01:12:03,280
covariance matrices

1552
01:12:03,280 --> 01:12:07,040
you want to go to correlation matrices

1553
01:12:07,040 --> 01:12:07,040
but what would come next

1554
01:12:07,120 --> 01:12:13,600
uh next so there there are many examples

1555
01:12:13,600 --> 01:12:16,159
of um orbit spaces so for example we can

1556
01:12:16,159 --> 01:12:19,679
think of the

1557
01:12:19,679 --> 01:12:20,640
uh the eigenvalue multiplicity can

1558
01:12:20,640 --> 01:12:22,880
be

1559
01:12:22,880 --> 01:12:24,320
so in in on diagonal matrices for

1560
01:12:24,320 --> 01:12:26,560
example first

1561
01:12:26,560 --> 01:12:28,960
uh we can see it as an orbit space uh

1562
01:12:28,960 --> 01:12:31,600
the orbit space of spd matrices

1563
01:12:31,600 --> 01:12:35,520
quotiented by the orthogonal group

1564
01:12:35,520 --> 01:12:37,440
uh and so the the fact is that the the

1565
01:12:37,440 --> 01:12:38,640
stabilizer

1566
01:12:38,640 --> 01:12:41,280
in the

1567
01:12:41,280 --> 01:12:43,120
in the orthogonal group depends

1568
01:12:43,120 --> 01:12:45,280
on the the multiplicity of the

1569
01:12:45,280 --> 01:12:47,120
eigenvalues right

1570
01:12:47,120 --> 01:12:49,360
so for example if you have all distinct

1571
01:12:49,360 --> 01:12:52,400
eigenvalues you have only

1572
01:12:52,400 --> 01:12:54,640
the choice of -1 1 on each

1573
01:12:54,640 --> 01:12:56,159
diagonal entry but if you take the

1574
01:12:56,159 --> 01:12:58,239
identity matrix you have all the

1575
01:12:58,239 --> 01:13:00,880
orthogonal group that is

1576
01:13:00,880 --> 01:13:03,360
that stabilizes the

1577
01:13:03,360 --> 01:13:03,360
matrix

1578
01:13:03,440 --> 01:13:08,320
so yes so this is an orbit space uh that

1579
01:13:08,320 --> 01:13:09,520
was actually

1580
01:13:09,520 --> 01:13:11,760
uh

1581
01:13:11,760 --> 01:13:16,320
studied partially i think

1582
01:13:16,320 --> 01:13:19,760
uh in a paper uh in 17 from

1583
01:13:19,760 --> 01:13:23,360
wasser and colleagues

1584
01:13:23,360 --> 01:13:25,199
both on the diagonal matrices and the

1585
01:13:25,199 --> 01:13:27,199
spd matrices

1586
01:13:27,199 --> 01:13:32,080
so for example i think that

1587
01:13:32,080 --> 01:13:34,000
probably we could generalize this to um

1588
01:13:34,000 --> 01:13:36,239
to

1589
01:13:36,239 --> 01:13:39,760
positive semi-definite matrices it was

1590
01:13:39,760 --> 01:13:42,480
done only on spd matrices i think we can

1591
01:13:42,480 --> 01:13:45,360
include uh in this framework

1592
01:13:45,360 --> 01:13:47,679
uh the the zero

1593
01:13:47,679 --> 01:13:49,679
eigenvalues i think it shouldn't be too

1594
01:13:49,679 --> 01:13:51,760
difficult

1595
01:13:51,760 --> 01:13:54,480
and then um

1596
01:13:54,480 --> 01:13:56,400
i i don't have other examples that come

1597
01:13:56,400 --> 01:13:59,280
to my mind right now but i think i'm

1598
01:13:59,280 --> 01:14:03,040
using this one some other examples

1599
01:14:03,040 --> 01:14:05,360
in the conclusion of problem spaces

1600
01:14:05,360 --> 01:14:05,360
okay

1601
01:14:08,080 --> 01:14:12,719
so we move to

1602
01:14:12,719 --> 01:14:15,120
the second reporter mark arnold

1603
01:14:15,120 --> 01:14:18,560
who is in person i don't know whether he

1604
01:14:18,560 --> 01:14:18,560
should step um

1605
01:14:19,600 --> 01:14:23,520
so let me take just one second to change

1606
01:14:23,520 --> 01:14:27,159
the mic

1607
01:14:27,159 --> 01:14:27,159
okay um

1608
01:14:50,170 --> 01:14:53,310
[Music]

1609
01:14:55,360 --> 01:15:01,920
yes or or i would suggest that you just

1610
01:15:01,920 --> 01:15:01,920
uh speak next to jan

1611
01:15:02,239 --> 01:15:07,480
what about that

1612
01:15:07,480 --> 01:15:07,480
okay

1613
01:15:10,880 --> 01:15:16,320
so that it's it's a real conversation

1614
01:15:16,320 --> 01:15:19,840
yeah and actually you can also see uh

1615
01:15:19,840 --> 01:15:19,840
not because exactly

1616
01:15:22,080 --> 01:15:27,520
okay can you lean now

1617
01:15:27,520 --> 01:15:27,520
yes that's much better thank you

1618
01:15:28,830 --> 01:15:31,970
[Music]

1619
01:15:32,560 --> 01:15:36,239
so

1620
01:15:36,239 --> 01:15:38,560
i'd like to say also that

1621
01:15:38,560 --> 01:15:41,280
the manuscript is a very impressive work

1622
01:15:41,280 --> 01:15:42,080
and very instructive and big pleasure

1623
01:15:42,080 --> 01:15:45,120
and

1624
01:15:45,120 --> 01:15:46,159
it is certainly certainly here reference

1625
01:15:46,159 --> 01:15:47,520
work

1626
01:15:47,520 --> 01:15:49,600
for many people

1627
01:15:49,600 --> 01:15:52,800
very very much

1628
01:15:52,800 --> 01:15:53,760
so i have several questions my first

1629
01:15:53,760 --> 01:15:56,480
question is

1630
01:15:56,480 --> 01:15:58,880
very much to relate to the second

1631
01:15:58,880 --> 01:15:58,880
question

1632
01:16:02,400 --> 01:16:06,900
but it's not from a statistical point of

1633
01:16:06,900 --> 01:16:09,920
view

1634
01:16:09,920 --> 01:16:12,080
[Music]

1635
01:16:12,080 --> 01:16:14,640
i want to skip more for from a

1636
01:16:14,640 --> 01:16:16,030
statistical point of view

1637
01:16:16,030 --> 01:16:19,040
what

1638
01:16:19,040 --> 01:16:20,640
[Music]

1639
01:16:20,640 --> 01:16:22,480
performance of

1640
01:16:22,480 --> 01:16:24,880
this matters

1641
01:16:24,880 --> 01:16:26,240
which may take you chosen

1642
01:16:26,240 --> 01:16:27,440
for instance we know that

1643
01:16:27,440 --> 01:16:30,400
[Music]

1644
01:16:30,400 --> 01:16:30,400
several examples

1645
01:16:31,199 --> 01:16:36,480
hyperbolic geometry helps

1646
01:16:36,480 --> 01:16:39,199
for classification for better results in

1647
01:16:39,199 --> 01:16:43,840
persecution so do you have answers from

1648
01:16:43,840 --> 01:16:46,640
a statistical point of view or improving

1649
01:16:46,640 --> 01:16:46,640
efficiency

1650
01:16:46,800 --> 01:16:48,640
um

1651
01:16:48,640 --> 01:16:50,960
so

1652
01:16:50,960 --> 01:16:51,760
it's true that it's a field that i

1653
01:16:51,760 --> 01:16:54,239
know

1654
01:16:54,239 --> 01:16:55,199
a bit less so

1655
01:16:55,199 --> 01:16:58,400
um

1656
01:16:58,400 --> 01:17:00,560
i i would say first that uh

1657
01:17:00,560 --> 01:17:02,400
if we take only define invariant metric

1658
01:17:02,400 --> 01:17:04,480
the login and the other search time

1659
01:17:04,480 --> 01:17:06,800
metric we can discriminate between the

1660
01:17:06,800 --> 01:17:08,719
three uh with the curator uh the

1661
01:17:08,719 --> 01:17:11,360
defining giant metric has negative

1662
01:17:11,360 --> 01:17:13,760
curvature logic feeding slack and the

1663
01:17:13,760 --> 01:17:14,640
subscribe is positive occur

1664
01:17:14,640 --> 01:17:16,960
so

1665
01:17:16,960 --> 01:17:19,360
probably changes things i know that the

1666
01:17:19,360 --> 01:17:21,600
curvature is very important in

1667
01:17:21,600 --> 01:17:24,239
statistics and statistical operations

1668
01:17:24,239 --> 01:17:26,400
but you work much more than on this

1669
01:17:26,400 --> 01:17:29,520
field

1670
01:17:29,520 --> 01:17:31,040
so it's the first element of answer then

1671
01:17:31,040 --> 01:17:34,480
i

1672
01:17:34,480 --> 01:17:36,560
i don't know if we can say

1673
01:17:36,560 --> 01:17:39,600
more things from a theoretical point of

1674
01:17:39,600 --> 01:17:40,560
view um

1675
01:17:40,560 --> 01:17:41,920
but

1676
01:17:41,920 --> 01:17:43,600
not another

1677
01:17:43,600 --> 01:17:47,520
direction

1678
01:17:47,520 --> 01:17:49,440
after these senses is also to

1679
01:17:49,440 --> 01:17:51,520
to test all these uh

1680
01:17:51,520 --> 01:17:54,400
all these metrics uh on data and

1681
01:17:54,400 --> 01:17:56,800
actually uh we started to do it uh with

1682
01:17:56,800 --> 01:17:59,120
the

1683
01:17:59,120 --> 01:17:59,120
panel

1684
01:18:01,120 --> 01:18:07,360
and so he wanted to to compare uh all

1685
01:18:07,360 --> 01:18:10,560
the other metrics from um

1686
01:18:10,560 --> 01:18:13,040
from a data point of view and so i think

1687
01:18:13,040 --> 01:18:15,199
probably i didn't think about it before

1688
01:18:15,199 --> 01:18:15,520
but maybe we could integrate

1689
01:18:15,520 --> 01:18:17,840
um

1690
01:18:17,840 --> 01:18:19,679
[Music]

1691
01:18:19,679 --> 01:18:21,520
experiments from a statistical point of

1692
01:18:21,520 --> 01:18:24,480
view in this uh

1693
01:18:24,480 --> 01:18:25,440
comparison benchmark of all overlappings

1694
01:18:25,440 --> 01:18:29,280
and see

1695
01:18:29,280 --> 01:18:30,880
what are the differences on statistical

1696
01:18:30,880 --> 01:18:32,800
results

1697
01:18:32,800 --> 01:18:35,600
if you have

1698
01:18:35,600 --> 01:18:37,120
continuous families with a

1699
01:18:37,120 --> 01:18:40,159
double parameter

1700
01:18:40,159 --> 01:18:40,159
you can imagine to

1701
01:18:53,040 --> 01:18:57,600
conversion at the speed of convergence

1702
01:18:57,600 --> 01:18:59,840
or

1703
01:18:59,840 --> 01:19:02,840
yeah yeah we can we could test all of

1704
01:19:02,840 --> 01:19:02,840
these

1705
01:19:03,710 --> 01:19:06,880
[Music]

1706
01:19:08,320 --> 01:19:12,640
second question from consensus

1707
01:19:12,640 --> 01:19:14,880
[Music]

1708
01:19:14,880 --> 01:19:18,960
several constructions you made where you

1709
01:19:18,960 --> 01:19:18,960
have the basically

1710
01:19:20,159 --> 01:19:23,159
okay

1711
01:19:25,800 --> 01:19:34,080
[Music]

1712
01:19:34,480 --> 01:19:42,319
is

1713
01:19:42,319 --> 01:19:42,319
[Music]

1714
01:20:02,880 --> 01:20:06,000
so my question is

1715
01:20:06,080 --> 01:20:12,400
more examples of principal bundles

1716
01:20:12,400 --> 01:20:15,280
if you have a class in the brexit random

1717
01:20:15,280 --> 01:20:17,800
are you able to

1718
01:20:17,800 --> 01:20:22,699
write it as a product

1719
01:20:22,699 --> 01:20:22,699
[Music]

1720
01:20:27,810 --> 01:20:30,889
[Music]

1721
01:20:35,280 --> 01:20:38,560
so here yes because

1722
01:20:38,560 --> 01:20:41,199
actually it's

1723
01:20:41,199 --> 01:20:43,040
it's a bit uh false principle bundles

1724
01:20:43,040 --> 01:20:44,239
because they are actually product

1725
01:20:44,239 --> 01:20:48,159
manifolds

1726
01:20:48,159 --> 01:20:50,480
and so in this very example we could uh

1727
01:20:50,480 --> 01:20:52,480
easily have a horizontal part and a

1728
01:20:52,480 --> 01:20:55,440
vertical part because it's only the

1729
01:20:55,440 --> 01:20:57,679
projections to the to the mentals

1730
01:20:57,679 --> 01:21:00,159
but i think it's not true in general

1731
01:21:00,159 --> 01:21:00,159
it's not true

1732
01:21:03,260 --> 01:21:06,430
[Music]

1733
01:21:12,080 --> 01:21:17,149
foreign

1734
01:21:17,149 --> 01:21:17,149
[Music]

1735
01:21:30,790 --> 01:21:33,930
[Music]

1736
01:21:34,000 --> 01:21:39,040
it is very useful to find the horizontal

1737
01:21:39,040 --> 01:21:41,199
distance because if you have any

1738
01:21:41,199 --> 01:21:46,080
geodesic with the particles

1739
01:21:46,080 --> 01:21:46,080
you decompose it as a horizontal path

1740
01:21:46,239 --> 01:21:51,920
in some games that can pass or you get

1741
01:21:51,920 --> 01:21:56,679
a shorter pass

1742
01:21:56,679 --> 01:21:56,679
and then you get two points

1743
01:22:08,880 --> 01:22:13,920
so

1744
01:22:13,920 --> 01:22:16,800
i'm not sure to understand but here i

1745
01:22:16,800 --> 01:22:18,400
think so you mean an algorithm to to go

1746
01:22:18,400 --> 01:22:20,639
from uh

1747
01:22:20,639 --> 01:22:23,520
general geodesics to the horizontal ones

1748
01:22:23,520 --> 01:22:25,760
in the principal bundle yeah okay so uh

1749
01:22:25,760 --> 01:22:28,719
in in the example of the quotient

1750
01:22:28,719 --> 01:22:30,960
defined metric we have uh the horizontal

1751
01:22:30,960 --> 01:22:32,239
geodesics in first form

1752
01:22:32,239 --> 01:22:34,639
so uh

1753
01:22:34,639 --> 01:22:36,639
i would say that um

1754
01:22:36,639 --> 01:22:37,520
yeah we already have them

1755
01:22:37,520 --> 01:22:40,639
and

1756
01:22:40,639 --> 01:22:42,400
and uh moreover it's a it's a it's a

1757
01:22:42,400 --> 01:22:43,760
product

1758
01:22:43,760 --> 01:22:44,800
manifold so

1759
01:22:44,800 --> 01:22:47,520
um

1760
01:22:47,520 --> 01:22:50,080
i think in this manifold it's very

1761
01:22:50,080 --> 01:22:51,199
easy but then

1762
01:22:51,199 --> 01:22:52,880
uh

1763
01:22:52,880 --> 01:22:55,040
for example for the new build research

1764
01:22:55,040 --> 01:22:57,600
time metric it's also a portion space

1765
01:22:57,600 --> 01:22:59,840
but it's not the manifold because the

1766
01:22:59,840 --> 01:23:00,800
stabilizers have

1767
01:23:00,800 --> 01:23:01,760
different

1768
01:23:01,760 --> 01:23:03,760
are

1769
01:23:03,760 --> 01:23:09,040
not uh

1770
01:23:09,040 --> 01:23:12,159
and so here uh i wonder if uh the

1771
01:23:12,159 --> 01:23:13,120
these types of algorithm you mentioned

1772
01:23:13,120 --> 01:23:16,239
could

1773
01:23:16,239 --> 01:23:17,600
be applied to these orbit spaces

1774
01:23:17,600 --> 01:23:19,750
that are not

1775
01:23:19,750 --> 01:23:22,920
bundles but

1776
01:23:22,920 --> 01:23:22,920
[Music]

1777
01:23:32,400 --> 01:23:35,280
framework to

1778
01:23:35,280 --> 01:23:37,600
the

1779
01:23:37,600 --> 01:23:39,679
stratified framework

1780
01:23:39,679 --> 01:23:43,520
the is it still working all the

1781
01:23:43,520 --> 01:23:48,440
algorithms that we have in the labels

1782
01:23:48,440 --> 01:23:48,440
are they still working in workspaces

1783
01:23:58,080 --> 01:24:01,400
last question

1784
01:24:12,800 --> 01:24:18,239
um i know definition

1785
01:24:18,239 --> 01:24:20,080
but then

1786
01:24:20,080 --> 01:24:21,840
so it's

1787
01:24:21,840 --> 01:24:24,320
basically we replace the inner product

1788
01:24:24,320 --> 01:24:27,280
by uh

1789
01:24:27,280 --> 01:24:31,239
and so i think it it also induces a

1790
01:24:31,239 --> 01:24:31,239
distance on the

1791
01:24:56,159 --> 01:24:59,679
and

1792
01:24:59,679 --> 01:25:02,159
i i don't know how

1793
01:25:02,159 --> 01:25:04,400
all this generalizes to

1794
01:25:04,400 --> 01:25:06,639
to instrumetrix because i i don't really

1795
01:25:06,639 --> 01:25:09,760
know the essence of

1796
01:25:09,760 --> 01:25:09,760
these kinds of things

1797
01:25:10,719 --> 01:25:13,840
thank you

1798
01:25:14,000 --> 01:25:19,760
and we cannot hear your order

1799
01:25:19,760 --> 01:25:21,920
yeah thank you very much um

1800
01:25:21,920 --> 01:25:24,000
we are now moving to the other members

1801
01:25:24,000 --> 01:25:26,000
of the jury and we will start with

1802
01:25:26,000 --> 01:25:28,080
professor battia batia

1803
01:25:28,080 --> 01:25:32,520
and i would suggest that you also come

1804
01:25:32,520 --> 01:25:32,520
forward and speak to them

1805
01:25:41,520 --> 01:25:46,159
and please speak close to the microphone

1806
01:25:46,159 --> 01:25:47,940
yeah maybe you you can come here if you

1807
01:25:47,940 --> 01:25:49,440
want to

1808
01:25:49,440 --> 01:25:51,600
[Music]

1809
01:25:51,600 --> 01:25:55,120
so that was a very beautifully prepared

1810
01:25:55,120 --> 01:25:57,040
presentation i enjoyed it a lot

1811
01:25:57,040 --> 01:25:58,639
while reading the thesis i had some

1812
01:25:58,639 --> 01:26:00,960
questions maybe i didn't understand a

1813
01:26:00,960 --> 01:26:03,199
few things so

1814
01:26:03,199 --> 01:26:05,360
we have to go to paid numbers can we do

1815
01:26:05,360 --> 01:26:05,360
that

1816
01:26:06,840 --> 01:26:12,000
absolutely

1817
01:26:12,000 --> 01:26:12,000
so page 24

1818
01:26:12,080 --> 01:26:17,280
you seem to say gln you are reducing to

1819
01:26:17,280 --> 01:26:21,560
sln into positively

1820
01:26:21,560 --> 01:26:21,560
trying to remind reading it wrong

1821
01:26:22,080 --> 01:26:27,679
um

1822
01:26:27,679 --> 01:26:30,400
yes uh so i'm slightly puzzled

1823
01:26:30,400 --> 01:26:32,239
if you factor out sln you should get all

1824
01:26:32,239 --> 01:26:34,639
real numbers

1825
01:26:34,639 --> 01:26:36,719
so what what is the mystery here uh the

1826
01:26:36,719 --> 01:26:39,199
mystery here is uh

1827
01:26:39,199 --> 01:26:42,080
that actually um

1828
01:26:42,080 --> 01:26:43,920
we are talking about uh matrix uh that

1829
01:26:43,920 --> 01:26:44,960
are invariant and there are these two

1830
01:26:44,960 --> 01:26:47,520
groups

1831
01:26:47,520 --> 01:26:48,320
uh these blue functions and actually all

1832
01:26:48,320 --> 01:26:51,280
the

1833
01:26:51,280 --> 01:26:54,320
gln invariant matrix

1834
01:26:54,320 --> 01:26:56,400
are gn plus and variant matrix but the

1835
01:26:56,400 --> 01:26:59,440
converse is also true

1836
01:26:59,440 --> 01:27:00,159
and so that's why with r plus and sln we

1837
01:27:00,159 --> 01:27:02,239
can

1838
01:27:02,239 --> 01:27:04,320
reconvert gl plus

1839
01:27:04,320 --> 01:27:06,239
and then since the matrix is gel plus

1840
01:27:06,239 --> 01:27:08,320
invariant

1841
01:27:08,320 --> 01:27:11,440
how about the o n you get

1842
01:27:11,440 --> 01:27:12,480
positive diagonal only or

1843
01:27:12,480 --> 01:27:15,679
sorry

1844
01:27:15,679 --> 01:27:18,000
gln modulo o n

1845
01:27:18,000 --> 01:27:21,600
this would suggest its positive diagonal

1846
01:27:21,600 --> 01:27:23,600
next is that right or again uh no here

1847
01:27:23,600 --> 01:27:25,440
i'm really talking about

1848
01:27:25,440 --> 01:27:27,600
families that are invariant and there

1849
01:27:27,600 --> 01:27:29,679
are these group actions so i'm not

1850
01:27:29,679 --> 01:27:32,320
talking about the groups itself

1851
01:27:32,320 --> 01:27:32,320
themselves

1852
01:27:38,960 --> 01:27:44,120
for the first pair for the second player

1853
01:27:44,120 --> 01:27:44,120
uh

1854
01:27:47,360 --> 01:27:51,520
it generates the the glm

1855
01:27:52,080 --> 01:27:55,520
but if you your question was if we

1856
01:27:55,520 --> 01:27:58,239
portioned

1857
01:27:58,239 --> 01:28:00,480
by the article

1858
01:28:00,480 --> 01:28:02,719
should get positive matrices right

1859
01:28:02,719 --> 01:28:02,719
yes

1860
01:28:03,360 --> 01:28:07,199
okay chapter two now there is a lot of

1861
01:28:07,199 --> 01:28:08,880
literature which you seem to have

1862
01:28:08,880 --> 01:28:10,960
ignored

1863
01:28:10,960 --> 01:28:13,040
for example there are lots of papers by

1864
01:28:13,040 --> 01:28:16,480
chandra davis

1865
01:28:16,480 --> 01:28:18,960
uh direct rotations of subspaces

1866
01:28:18,960 --> 01:28:21,040
and work of mathematicians in fact

1867
01:28:21,040 --> 01:28:24,239
and others with reports

1868
01:28:24,239 --> 01:28:26,239
so you should take that into yeah no i i

1869
01:28:26,239 --> 01:28:28,880
i confuse you agree that

1870
01:28:28,880 --> 01:28:31,440
in the chapter two uh i was

1871
01:28:31,440 --> 01:28:32,719
trying to get some results that i needed

1872
01:28:32,719 --> 01:28:35,280
for later

1873
01:28:35,280 --> 01:28:37,360
and i tried to go into this literature

1874
01:28:37,360 --> 01:28:42,239
that i didn't really know

1875
01:28:42,239 --> 01:28:42,239
so i i cite some some words but yeah i

1876
01:28:43,280 --> 01:28:49,760
yeah just to point that out

1877
01:28:49,760 --> 01:28:53,199
now page 47

1878
01:28:53,199 --> 01:28:54,480
definition 3.1 it's i don't understand

1879
01:28:54,480 --> 01:28:58,000
it

1880
01:28:58,000 --> 01:29:03,040
you say a module is a vector space

1881
01:29:03,040 --> 01:29:03,040
w on which g acts continuously such as

1882
01:29:03,760 --> 01:29:10,880
such as v so the the v is the one i i

1883
01:29:10,880 --> 01:29:12,480
introduce at the beginning of the

1884
01:29:12,480 --> 01:29:14,480
of the section

1885
01:29:14,480 --> 01:29:17,280
but maybe it's not clear

1886
01:29:17,280 --> 01:29:17,280
said like this

1887
01:29:18,800 --> 01:29:24,840
i wanted to say that the the v that is

1888
01:29:28,000 --> 01:29:30,639
yeah so this sentence first of all

1889
01:29:30,639 --> 01:29:34,000
doesn't make

1890
01:29:34,000 --> 01:29:35,440
it doesn't have any meaning in english

1891
01:29:35,440 --> 01:29:36,960
maybe you're translating something with

1892
01:29:36,960 --> 01:29:38,639
friends okay

1893
01:29:38,639 --> 01:29:40,480
but modules and vector spaces are

1894
01:29:40,480 --> 01:29:41,760
different right

1895
01:29:41,760 --> 01:29:43,679
yes

1896
01:29:43,679 --> 01:29:46,000
so you start by saying a module is a

1897
01:29:46,000 --> 01:29:47,440
vector space i think something is not

1898
01:29:47,440 --> 01:29:48,560
quite

1899
01:29:48,560 --> 01:29:51,199
right here

1900
01:29:51,199 --> 01:29:54,239
actually it was

1901
01:29:54,239 --> 01:29:56,639
i i read some literature where uh the

1902
01:29:56,639 --> 01:29:57,200
vector spaces on which uh there is an

1903
01:29:57,200 --> 01:29:59,840
introduction

1904
01:29:59,840 --> 01:30:00,580
[Music]

1905
01:30:00,580 --> 01:30:02,400
maybe

1906
01:30:02,400 --> 01:30:04,320
[Music]

1907
01:30:04,320 --> 01:30:06,800
so vector spaces are always over fields

1908
01:30:06,800 --> 01:30:08,159
and modules need not have fields as the

1909
01:30:08,159 --> 01:30:10,080
underlying

1910
01:30:10,080 --> 01:30:12,960
scalars so to say

1911
01:30:12,960 --> 01:30:15,120
the difference ah okay okay but yeah i'm

1912
01:30:15,120 --> 01:30:16,480
talking about the different things here

1913
01:30:16,480 --> 01:30:19,360
so yeah okay

1914
01:30:19,360 --> 01:30:20,719
there is a mis naming maybe but i i

1915
01:30:20,719 --> 01:30:23,679
wanted to

1916
01:30:23,679 --> 01:30:26,560
i wanted to find a very simple name to

1917
01:30:26,560 --> 01:30:27,440
have the vector spaces on which i don't

1918
01:30:27,440 --> 01:30:29,600
act

1919
01:30:29,600 --> 01:30:31,280
and i decided this needs correction i

1920
01:30:31,280 --> 01:30:35,520
think okay

1921
01:30:35,520 --> 01:30:37,679
chapter 3 i was really puzzled

1922
01:30:37,679 --> 01:30:39,920
does representation theory really help

1923
01:30:39,920 --> 01:30:41,760
here

1924
01:30:41,760 --> 01:30:44,480
is it just complicating things i i

1925
01:30:44,480 --> 01:30:45,440
wasn't sure so could you tell us

1926
01:30:45,440 --> 01:30:49,600
um

1927
01:30:49,600 --> 01:30:50,560
so actually the the the way i came into

1928
01:30:50,560 --> 01:30:53,199
this

1929
01:30:53,199 --> 01:30:55,840
was when i looked for all the the

1930
01:30:55,840 --> 01:30:58,320
permutation in variant inner products on

1931
01:30:58,320 --> 01:31:01,120
matrices

1932
01:31:01,120 --> 01:31:02,800
on the basis of square matrices

1933
01:31:02,800 --> 01:31:03,679
and

1934
01:31:03,679 --> 01:31:05,040
then

1935
01:31:05,040 --> 01:31:06,880
i was asked

1936
01:31:06,880 --> 01:31:08,880
the same question for the block

1937
01:31:08,880 --> 01:31:12,239
orthogonal in variance

1938
01:31:12,239 --> 01:31:15,679
and i found that actually we could

1939
01:31:15,679 --> 01:31:16,960
find a general way of deriving

1940
01:31:16,960 --> 01:31:18,880
all the inner products that are

1941
01:31:18,880 --> 01:31:21,199
endurance under a certain loop and

1942
01:31:21,199 --> 01:31:23,280
that's why i formalized all this with

1943
01:31:23,280 --> 01:31:26,880
the representation theory

1944
01:31:26,880 --> 01:31:31,480
i found it helping at least for me

1945
01:31:31,480 --> 01:31:31,480
but maybe it's not community

1946
01:31:31,760 --> 01:31:34,800
okay

1947
01:31:34,800 --> 01:31:34,800
yeah

1948
01:31:35,520 --> 01:31:42,000
okay take 70 again i have a serious

1949
01:31:42,000 --> 01:31:44,159
difficulty

1950
01:31:44,159 --> 01:31:46,480
you have two positive definite matrices

1951
01:31:46,480 --> 01:31:48,800
sigma and lambda

1952
01:31:48,800 --> 01:31:50,960
yes you are taking

1953
01:31:50,960 --> 01:31:53,280
their product

1954
01:31:53,280 --> 01:31:54,800
and then a square root what does that

1955
01:31:54,800 --> 01:31:56,880
mean that product is not positive

1956
01:31:56,880 --> 01:31:58,400
doesn't it

1957
01:31:58,400 --> 01:31:59,679
you seem to suggest it's positive

1958
01:31:59,679 --> 01:32:01,760
definite

1959
01:32:01,760 --> 01:32:03,520
no so um

1960
01:32:03,520 --> 01:32:06,320
so actually the

1961
01:32:06,320 --> 01:32:10,480
it's simply a notation that is explained

1962
01:32:10,480 --> 01:32:12,960
a bit later but i mean on the same line

1963
01:32:12,960 --> 01:32:14,239
so actually this sigma lambda

1964
01:32:14,239 --> 01:32:17,199
um

1965
01:32:17,199 --> 01:32:18,800
is congruent to uh

1966
01:32:18,800 --> 01:32:20,880
not from they're not congruent sorry

1967
01:32:20,880 --> 01:32:23,840
it's not going to preserve positivity

1968
01:32:23,840 --> 01:32:25,920
yeah it's similar sorry it's similar to

1969
01:32:25,920 --> 01:32:27,520
measurements and so the square root is

1970
01:32:27,520 --> 01:32:29,280
defined as

1971
01:32:29,280 --> 01:32:31,679
uh the

1972
01:32:31,679 --> 01:32:33,679
similar the

1973
01:32:33,679 --> 01:32:35,280
we apply the similarity we take the

1974
01:32:35,280 --> 01:32:37,760
square root and we apply the similarity

1975
01:32:37,760 --> 01:32:39,280
to that so this neutral direction

1976
01:32:39,280 --> 01:32:41,520
the way it's written

1977
01:32:41,520 --> 01:32:42,560
uh it's written in many papers like this

1978
01:32:42,560 --> 01:32:44,639
actually

1979
01:32:44,639 --> 01:32:47,360
okay i'm sorry then

1980
01:32:47,360 --> 01:32:48,880
so that's why i took it but uh it's true

1981
01:32:48,880 --> 01:32:50,960
that at first read

1982
01:32:50,960 --> 01:32:52,880
i was confused yeah very often they will

1983
01:32:52,880 --> 01:32:56,480
write like you know

1984
01:32:56,480 --> 01:32:58,080
every matrix with positive eigenvalues

1985
01:32:58,080 --> 01:33:01,840
has a square root with positive

1986
01:33:01,840 --> 01:33:05,920
eigenvalue yes so maybe

1987
01:33:05,920 --> 01:33:07,679
all right now chapter four

1988
01:33:07,679 --> 01:33:09,840
here i have a question which you might

1989
01:33:09,840 --> 01:33:12,239
like to think about

1990
01:33:12,239 --> 01:33:14,239
you characterize these o n invariant

1991
01:33:14,239 --> 01:33:18,000
inner products

1992
01:33:18,000 --> 01:33:21,280
as alpha trace x squared

1993
01:33:21,280 --> 01:33:23,840
plus beta times the square of trace x

1994
01:33:23,840 --> 01:33:23,840
right

1995
01:33:24,960 --> 01:33:30,480
tracer oh general just chapter four okay

1996
01:33:30,480 --> 01:33:32,880
so alpha so there's a term involving

1997
01:33:32,880 --> 01:33:34,800
trace of the square of x

1998
01:33:34,800 --> 01:33:36,960
and another term

1999
01:33:36,960 --> 01:33:38,960
the square of tracers

2000
01:33:38,960 --> 01:33:42,880
so one should notice that the first one

2001
01:33:42,880 --> 01:33:44,560
is the sum of squares of singular values

2002
01:33:44,560 --> 01:33:47,600
and the second one is

2003
01:33:47,600 --> 01:33:49,840
the square of the sum of eigenvalues

2004
01:33:49,840 --> 01:33:52,400
is there any philosophy behind this that

2005
01:33:52,400 --> 01:33:54,560
sin every such inner product should

2006
01:33:54,560 --> 01:33:57,040
depend only on singular values and

2007
01:33:57,040 --> 01:33:59,040
eigenvalues and nothing else

2008
01:33:59,040 --> 01:33:59,840
uh so these are the

2009
01:33:59,840 --> 01:34:01,520
the

2010
01:34:01,520 --> 01:34:04,080
the products that are invariant and

2011
01:34:04,080 --> 01:34:05,210
their orthogonal transformations

2012
01:34:05,210 --> 01:34:06,960
and so

2013
01:34:06,960 --> 01:34:10,560
[Music]

2014
01:34:10,560 --> 01:34:12,960
the first one singular values

2015
01:34:12,960 --> 01:34:15,920
and second one involving eigenvalues see

2016
01:34:15,920 --> 01:34:18,400
in in matrix analysis

2017
01:34:18,400 --> 01:34:20,719
there are unitarily invariant norms and

2018
01:34:20,719 --> 01:34:22,239
then weekly unitarily invariant norms so

2019
01:34:22,239 --> 01:34:23,360
you seem to be getting a mixture of the

2020
01:34:23,360 --> 01:34:25,280
two

2021
01:34:25,280 --> 01:34:28,080
but is there any explanation that these

2022
01:34:28,080 --> 01:34:29,440
are the only two terms which should

2023
01:34:29,440 --> 01:34:31,600
come up

2024
01:34:31,600 --> 01:34:34,000
but here we are talking about symmetric

2025
01:34:34,000 --> 01:34:36,719
matrices

2026
01:34:36,719 --> 01:34:38,800
yeah yeah yeah but nevertheless the

2027
01:34:38,800 --> 01:34:40,719
symmetric matrices

2028
01:34:40,719 --> 01:34:43,360
the singular values are just the

2029
01:34:43,360 --> 01:34:45,119
absolute values of the eigenvalues yeah

2030
01:34:45,119 --> 01:34:47,040
but i'm curious why should only these

2031
01:34:47,040 --> 01:34:48,960
two things occur

2032
01:34:48,960 --> 01:34:51,360
i feel like fundamental has a theorem

2033
01:34:51,360 --> 01:34:54,080
about unitarily invariant norms must

2034
01:34:54,080 --> 01:34:56,159
depend only on singular values

2035
01:34:56,159 --> 01:35:00,800
that's the first thing it moves

2036
01:35:00,800 --> 01:35:02,960
but here this the eigenvalues and the

2037
01:35:02,960 --> 01:35:04,719
my question is that have you thought

2038
01:35:04,719 --> 01:35:07,920
about why should only these two things

2039
01:35:07,920 --> 01:35:09,280
occur and nothing else

2040
01:35:09,280 --> 01:35:10,080
i think it's something which needs a

2041
01:35:10,080 --> 01:35:12,159
little

2042
01:35:12,159 --> 01:35:14,880
thinking and explanation

2043
01:35:14,880 --> 01:35:16,880
that any o n invariant inner product on

2044
01:35:16,880 --> 01:35:18,880
symmetric matrices on symmetric matrices

2045
01:35:18,880 --> 01:35:20,719
should be a function only of singular

2046
01:35:20,719 --> 01:35:22,159
values and items

2047
01:35:22,159 --> 01:35:23,679
um

2048
01:35:23,679 --> 01:35:26,639
yeah i didn't

2049
01:35:26,639 --> 01:35:26,639
phrase it like this

2050
01:35:27,119 --> 01:35:29,840
okay

2051
01:35:30,159 --> 01:35:36,719
now you say in page 83

2052
01:35:36,719 --> 01:35:39,280
that the use of co-metric has been very

2053
01:35:39,280 --> 01:35:41,440
helpful in computations

2054
01:35:41,440 --> 01:35:43,199
any examples you have done it didn't

2055
01:35:43,199 --> 01:35:44,960
seem to say that so

2056
01:35:44,960 --> 01:35:47,440
just a question here

2057
01:35:47,440 --> 01:35:48,800
uh here it's really based on the fact

2058
01:35:48,800 --> 01:35:52,080
that uh

2059
01:35:52,080 --> 01:35:54,000
in german stats we implemented and i

2060
01:35:54,000 --> 01:35:56,480
didn't do it myself but it is

2061
01:35:56,480 --> 01:35:58,320
implemented like this by integrating

2062
01:35:58,320 --> 01:36:00,960
the hamiltonian flow

2063
01:36:00,960 --> 01:36:02,880
and it requires the metric

2064
01:36:02,880 --> 01:36:06,480
so metric has been helpful the examples

2065
01:36:06,480 --> 01:36:06,480
were not given so i would just do this

2066
01:36:08,000 --> 01:36:12,000
and

2067
01:36:12,000 --> 01:36:13,440
two general questions

2068
01:36:13,440 --> 01:36:16,320
uh

2069
01:36:16,320 --> 01:36:18,480
everything you say is about real

2070
01:36:18,480 --> 01:36:20,239
symmetric positive what's the problem in

2071
01:36:20,239 --> 01:36:21,360
going to complex matrices everything

2072
01:36:21,360 --> 01:36:23,920
should work

2073
01:36:23,920 --> 01:36:25,840
uh it's true that it's a very

2074
01:36:25,840 --> 01:36:28,080
interesting direction that i i didn't

2075
01:36:28,080 --> 01:36:30,560
investigate because i i started on spd

2076
01:36:30,560 --> 01:36:32,639
matrices and so yeah i would like to

2077
01:36:32,639 --> 01:36:34,400
almost everything you are doing

2078
01:36:34,400 --> 01:36:38,600
i think

2079
01:36:38,600 --> 01:36:38,600
yeah most of what i did

2080
01:36:39,679 --> 01:36:46,880
now chapter 9 you seem to suggest

2081
01:36:46,880 --> 01:36:49,119
that the reason for introducing those

2082
01:36:49,119 --> 01:36:51,760
versus time distance is simply that it

2083
01:36:51,760 --> 01:36:54,560
can be defined over

2084
01:36:54,560 --> 01:36:58,000
all positive semi-definite matrices

2085
01:36:58,000 --> 01:37:01,440
the reason for introducing

2086
01:37:01,440 --> 01:37:03,920
i think that's not quite right

2087
01:37:03,920 --> 01:37:07,440
the intrinsic reasons for that metric

2088
01:37:07,440 --> 01:37:08,639
optimal transport uh quantum information

2089
01:37:08,639 --> 01:37:11,520
where you have

2090
01:37:11,520 --> 01:37:12,639
traced your introduction okay it seems

2091
01:37:12,639 --> 01:37:14,159
to say that

2092
01:37:14,159 --> 01:37:16,400
you know the fine invariant metric

2093
01:37:16,400 --> 01:37:18,560
doesn't work for

2094
01:37:18,560 --> 01:37:19,760
low rank matrices lower rank and this

2095
01:37:19,760 --> 01:37:22,000
one does

2096
01:37:22,000 --> 01:37:24,320
i don't think that's the the main reason

2097
01:37:24,320 --> 01:37:27,199
for the production

2098
01:37:27,199 --> 01:37:29,360
so it's a little amazing

2099
01:37:29,360 --> 01:37:31,679
and having introduced all these metrics

2100
01:37:31,679 --> 01:37:34,080
uh oh just one thing which

2101
01:37:34,080 --> 01:37:36,320
links with what mark was saying earlier

2102
01:37:36,320 --> 01:37:39,520
since the metric you are talking on page

2103
01:37:39,520 --> 01:37:40,800
170 of length space

2104
01:37:40,800 --> 01:37:42,800
i think already you are talking of

2105
01:37:42,800 --> 01:37:45,440
singular matrix there because

2106
01:37:45,440 --> 01:37:48,719
what's the length space can you explain

2107
01:37:48,719 --> 01:37:51,040
yeah so a length space is a space

2108
01:37:51,040 --> 01:37:53,840
where so on the metric space we can

2109
01:37:53,840 --> 01:37:56,639
define the intrinsic metric

2110
01:37:56,639 --> 01:37:58,719
uh which is the metric that measures the

2111
01:37:58,719 --> 01:38:01,040
length of bats

2112
01:38:01,040 --> 01:38:04,080
and if it coincides with the initial

2113
01:38:04,080 --> 01:38:05,840
distance the metric uh the space is

2114
01:38:05,840 --> 01:38:07,920
called the length yeah so you are saying

2115
01:38:07,920 --> 01:38:09,760
that there is a curve which is not a

2116
01:38:09,760 --> 01:38:11,520
straight line

2117
01:38:11,520 --> 01:38:13,520
but whose length is the same as that of

2118
01:38:13,520 --> 01:38:16,960
the straight line

2119
01:38:16,960 --> 01:38:18,400
right is that what we are saying

2120
01:38:18,400 --> 01:38:21,119
so for example

2121
01:38:21,119 --> 01:38:24,000
we can take the sphere uh the

2122
01:38:24,000 --> 01:38:26,560
if if we consider the the embedding

2123
01:38:26,560 --> 01:38:28,880
distance the euclidean distance

2124
01:38:28,880 --> 01:38:30,719
and then if you consider the distance

2125
01:38:30,719 --> 01:38:33,040
induced by the fats

2126
01:38:33,040 --> 01:38:35,600
they are different yes and so they don't

2127
01:38:35,600 --> 01:38:36,960
coincide yes so that's exactly what i'm

2128
01:38:36,960 --> 01:38:38,639
saying so length space and your

2129
01:38:38,639 --> 01:38:40,800
definition

2130
01:38:40,800 --> 01:38:42,960
means there is a something which is a

2131
01:38:42,960 --> 01:38:45,119
geodesic not a straight line

2132
01:38:45,119 --> 01:38:47,679
whose length is the same as that of the

2133
01:38:47,679 --> 01:38:51,360
straight line

2134
01:38:51,360 --> 01:38:51,360
is that your definition of length space

2135
01:38:52,239 --> 01:38:57,520
so maybe i should uh

2136
01:38:57,520 --> 01:38:59,440
okay i just want to make a comment the

2137
01:38:59,440 --> 01:39:02,480
interest of time that

2138
01:39:02,480 --> 01:39:05,199
you seem to be talking of a space

2139
01:39:05,199 --> 01:39:07,440
in which there is a curve

2140
01:39:07,440 --> 01:39:10,000
embedded in the euclidean space there is

2141
01:39:10,000 --> 01:39:11,760
a curve which is not a straight line

2142
01:39:11,760 --> 01:39:13,600
but its length is the same as that of

2143
01:39:13,600 --> 01:39:15,440
the straight line joining the endpoints

2144
01:39:15,440 --> 01:39:18,320
is that your definition no it's not my

2145
01:39:18,320 --> 01:39:20,000
definition definitely but maybe it's uh

2146
01:39:20,000 --> 01:39:22,880
that's the impression i got from your

2147
01:39:22,880 --> 01:39:24,719
what you say about lens test

2148
01:39:24,719 --> 01:39:26,639
and i just want to say that

2149
01:39:26,639 --> 01:39:28,880
what mark has i mean that would not be

2150
01:39:28,880 --> 01:39:31,360
possible in romanian geometry it has to

2151
01:39:31,360 --> 01:39:33,040
be since the geometry

2152
01:39:33,040 --> 01:39:36,800
any good examples of such a space that

2153
01:39:36,800 --> 01:39:41,199
you look to look at i linked things yes

2154
01:39:41,199 --> 01:39:44,000
so any any romanian manifold is

2155
01:39:44,000 --> 01:39:45,760
now that's what's confusing me because

2156
01:39:45,760 --> 01:39:46,800
so take this here

2157
01:39:46,800 --> 01:39:49,520
the

2158
01:39:49,520 --> 01:39:51,360
your district joining any two points

2159
01:39:51,360 --> 01:39:53,280
will not have the same length as that of

2160
01:39:53,280 --> 01:39:56,239
the straight line joining the endpoints

2161
01:39:56,239 --> 01:39:58,080
right yeah it depends on which metrics

2162
01:39:58,080 --> 01:40:00,000
you put on your usual metrics so any

2163
01:40:00,000 --> 01:40:02,320
romanian manifold is not and then space

2164
01:40:02,320 --> 01:40:04,320
according to what i understood

2165
01:40:04,320 --> 01:40:06,560
so if you take a remaining manifold and

2166
01:40:06,560 --> 01:40:08,480
take the remaining distance on it this

2167
01:40:08,480 --> 01:40:11,440
remaining distance is intrinsic because

2168
01:40:11,440 --> 01:40:12,320
it measures the length uh of fats

2169
01:40:12,320 --> 01:40:15,199
and so

2170
01:40:15,199 --> 01:40:16,880
the space is the next space

2171
01:40:16,880 --> 01:40:19,360
that that's what you understand the

2172
01:40:19,360 --> 01:40:22,800
definition here so yeah maybe i need to

2173
01:40:22,800 --> 01:40:24,400
clarify the definition

2174
01:40:24,400 --> 01:40:25,679
okay last question

2175
01:40:25,679 --> 01:40:28,239
so

2176
01:40:28,239 --> 01:40:32,159
having defined so many metrics have you

2177
01:40:32,159 --> 01:40:34,880
computed any special means and so on

2178
01:40:34,880 --> 01:40:37,440
so many fresher means were computed for

2179
01:40:37,440 --> 01:40:39,119
the the usual ones uh so defining

2180
01:40:39,119 --> 01:40:40,320
variant electric logic

2181
01:40:40,320 --> 01:40:43,280
at such time

2182
01:40:43,280 --> 01:40:46,960
uh then we tried to compute fresh amines

2183
01:40:46,960 --> 01:40:48,080
and other components yeah indirectly for

2184
01:40:48,080 --> 01:40:50,480
the

2185
01:40:50,480 --> 01:40:52,800
mixed power impedance metrics

2186
01:40:52,800 --> 01:40:54,159
um and so

2187
01:40:54,159 --> 01:40:56,080
yeah you have some problems yeah we

2188
01:40:56,080 --> 01:40:58,639
computed some but

2189
01:40:58,639 --> 01:41:00,960
it didn't give better specifically

2190
01:41:00,960 --> 01:41:04,600
classification results than

2191
01:41:04,600 --> 01:41:04,600
formulas or

2192
01:41:14,080 --> 01:41:22,480
um we move now to professor willingly

2193
01:41:22,480 --> 01:41:22,480
you also please come forward

2194
01:41:28,800 --> 01:41:31,800
okay

2195
01:41:33,360 --> 01:41:38,239
uh okay i'll ask probably

2196
01:41:38,239 --> 01:41:42,320
i've tried not not to use page before

2197
01:41:42,320 --> 01:41:43,360
all my movie totals which is

2198
01:41:43,360 --> 01:41:46,400
um

2199
01:41:46,400 --> 01:41:48,400
first question is about you

2200
01:41:48,400 --> 01:41:50,320
after you calculated that power

2201
01:41:50,320 --> 01:41:51,520
transport

2202
01:41:51,520 --> 01:41:53,920
as a

2203
01:41:53,920 --> 01:41:57,280
building with a skype metric when you

2204
01:41:57,280 --> 01:42:00,159
complicate it it resembles to the one

2205
01:42:00,159 --> 01:42:00,159
and the candle

2206
01:42:02,480 --> 01:42:06,000
my question is

2207
01:42:06,000 --> 01:42:08,719
do you think that just

2208
01:42:08,719 --> 01:42:10,960
president or is there any reason behind

2209
01:42:10,960 --> 01:42:10,960
that

2210
01:42:11,040 --> 01:42:16,960
that's a question that we tried to

2211
01:42:16,960 --> 01:42:17,560
investigate with lod uh so actually uh

2212
01:42:17,560 --> 01:42:19,280
the

2213
01:42:19,280 --> 01:42:22,080
[Music]

2214
01:42:22,080 --> 01:42:24,159
the research time uh space is the

2215
01:42:24,159 --> 01:42:25,280
quotient of square matrices by the

2216
01:42:25,280 --> 01:42:28,080
artwork

2217
01:42:28,080 --> 01:42:30,159
which is quite close to the definition

2218
01:42:30,159 --> 01:42:32,800
of kendall shaped spaces even if there

2219
01:42:32,800 --> 01:42:34,560
are some other normalizations in the

2220
01:42:34,560 --> 01:42:35,840
definition if i'm

2221
01:42:35,840 --> 01:42:37,040
correct

2222
01:42:37,040 --> 01:42:39,360
and so i

2223
01:42:39,360 --> 01:42:42,679
probably the the two

2224
01:42:42,679 --> 01:42:42,679
the two foreign

2225
01:42:42,800 --> 01:42:47,840
there is a reason behind but we were not

2226
01:42:47,840 --> 01:42:47,840
able to

2227
01:42:48,960 --> 01:42:53,840
understand properly

2228
01:42:53,840 --> 01:42:56,560
where the coincidence comes from because

2229
01:42:56,560 --> 01:42:58,480
there are also some differences in

2230
01:42:58,480 --> 01:43:00,239
between the two equations

2231
01:43:00,239 --> 01:43:04,080
uh so

2232
01:43:04,080 --> 01:43:04,080
it's it's not very clear why

2233
01:43:04,880 --> 01:43:08,239
um because i like the next question

2234
01:43:08,239 --> 01:43:09,760
still into

2235
01:43:09,760 --> 01:43:12,719
same spells

2236
01:43:12,719 --> 01:43:16,080
and you mentioned

2237
01:43:16,080 --> 01:43:19,040
operativity risk equations you always

2238
01:43:19,040 --> 01:43:19,040
have like time

2239
01:43:19,119 --> 01:43:23,480
in a lot of cases it is

2240
01:43:37,119 --> 01:43:40,000
so for example for the viewership time

2241
01:43:40,000 --> 01:43:41,160
uh

2242
01:43:41,160 --> 01:43:42,639
metric um

2243
01:43:42,639 --> 01:43:45,920
[Music]

2244
01:43:45,920 --> 01:43:48,639
in in spd matrices the cut time is

2245
01:43:48,639 --> 01:43:50,719
actually the time when the geodesic

2246
01:43:50,719 --> 01:43:53,199
leaves the space so when it hits the

2247
01:43:53,199 --> 01:43:56,080
boundary yeah

2248
01:43:56,080 --> 01:43:57,760
and in low rank matrices fixed fixed

2249
01:43:57,760 --> 01:44:00,320
rank k

2250
01:44:00,320 --> 01:44:01,440
it depends uh on the matrix you are

2251
01:44:01,440 --> 01:44:05,280
looking at

2252
01:44:05,280 --> 01:44:06,560
so sometimes it is when it reaches a

2253
01:44:06,560 --> 01:44:09,600
more singular

2254
01:44:09,600 --> 01:44:11,600
stratum and sometimes it's not the case

2255
01:44:11,600 --> 01:44:14,960
sometimes even

2256
01:44:14,960 --> 01:44:16,400
and it was proved by uh stillness

2257
01:44:16,400 --> 01:44:18,320
that

2258
01:44:18,320 --> 01:44:19,119
some geodesics

2259
01:44:19,119 --> 01:44:23,840
in

2260
01:44:23,840 --> 01:44:23,840
in lower uh strata are

2261
01:44:25,520 --> 01:44:29,679
close to infinity the

2262
01:44:29,679 --> 01:44:30,960
not the cut time but the the agility can

2263
01:44:30,960 --> 01:44:33,600
be defined

2264
01:44:33,600 --> 01:44:35,840
uh on r on the whole uh

2265
01:44:35,840 --> 01:44:38,560
real numbers

2266
01:44:38,560 --> 01:44:41,199
but of course there is a test time yeah

2267
01:44:41,199 --> 01:44:44,159
and but i mean that sometimes sometimes

2268
01:44:44,159 --> 01:44:46,400
the cut time is is not on the on the

2269
01:44:46,400 --> 01:44:47,520
lowest pattern it could be also in

2270
01:44:47,520 --> 01:44:50,599
really a

2271
01:44:50,599 --> 01:44:50,599
[Music]

2272
01:45:00,639 --> 01:45:06,080
it's always the case and on a lower

2273
01:45:06,080 --> 01:45:08,560
lower ranked matrices it's not always

2274
01:45:08,560 --> 01:45:08,560
the case

2275
01:45:09,119 --> 01:45:13,679
i mean i suppose probably i expect once

2276
01:45:13,679 --> 01:45:14,960
a year

2277
01:45:14,960 --> 01:45:19,280
question is

2278
01:45:19,280 --> 01:45:19,280
i'm talking about those uh bull ranges

2279
01:45:19,760 --> 01:45:25,800
in case what happened

2280
01:45:25,800 --> 01:45:25,800
when this great approach

2281
01:45:30,400 --> 01:45:36,320
so

2282
01:45:36,320 --> 01:45:38,880
i i didn't do it but uh in his paper on

2283
01:45:38,880 --> 01:45:40,880
the research time metric i think it's

2284
01:45:40,880 --> 01:45:42,960
quite clear

2285
01:45:42,960 --> 01:45:46,239
it's also related to the work of

2286
01:45:46,239 --> 01:45:49,119
takatsu i mean

2287
01:45:49,119 --> 01:45:52,000
and so actually the the curvature uh

2288
01:45:52,000 --> 01:45:53,600
goes to infinity from the boundary

2289
01:45:53,600 --> 01:45:56,320
um

2290
01:45:56,320 --> 01:45:58,800
it's simply because the

2291
01:45:58,800 --> 01:45:59,760
the cur in the formula of the curvature

2292
01:45:59,760 --> 01:46:02,239
uh

2293
01:46:02,239 --> 01:46:05,199
there are the two

2294
01:46:05,199 --> 01:46:08,080
there are two eigen values uh involved

2295
01:46:08,080 --> 01:46:12,560
uh at the denominator and so when you

2296
01:46:12,560 --> 01:46:14,639
reach the boundary the the two uh lower

2297
01:46:14,639 --> 01:46:16,400
eigenvalues and they go to

2298
01:46:16,400 --> 01:46:17,520
zero

2299
01:46:17,520 --> 01:46:20,719
and so the

2300
01:46:20,719 --> 01:46:20,719
the curvature uh

2301
01:46:20,880 --> 01:46:26,639
it's it's proved in the literature that

2302
01:46:26,639 --> 01:46:29,280
uh and it's true also in the in each

2303
01:46:29,280 --> 01:46:31,679
other stratum that the curvature goes to

2304
01:46:31,679 --> 01:46:34,320
infinity when you

2305
01:46:34,320 --> 01:46:34,320
go to lower

2306
01:46:34,560 --> 01:46:39,600
i would expect to adjust to those cases

2307
01:46:39,600 --> 01:46:42,639
sorry i expected this is possibly time

2308
01:46:42,639 --> 01:46:44,960
to influence possibilities

2309
01:46:44,960 --> 01:46:44,960
anyway

2310
01:46:47,600 --> 01:46:51,199
so the another discussion that we have

2311
01:46:51,199 --> 01:46:54,600
with

2312
01:46:54,600 --> 01:46:54,600
that um

2313
01:46:54,719 --> 01:46:58,800
uh

2314
01:46:58,800 --> 01:46:58,800
because this is the the

2315
01:46:59,040 --> 01:47:04,159
the sectional curvature but probably

2316
01:47:04,159 --> 01:47:08,000
there are some directions where where

2317
01:47:08,000 --> 01:47:09,199
the the curvature

2318
01:47:09,199 --> 01:47:12,320
stays

2319
01:47:12,320 --> 01:47:14,159
finite and remains finite and

2320
01:47:14,159 --> 01:47:17,119
the direction where it goes to the

2321
01:47:17,119 --> 01:47:19,280
boundary or here it becomes

2322
01:47:19,280 --> 01:47:22,880
infinite there is a curvature in the

2323
01:47:22,880 --> 01:47:25,520
stratum that is finite so there must be

2324
01:47:25,520 --> 01:47:28,159
a continuity between the two uh the two

2325
01:47:28,159 --> 01:47:28,159
curvatures

2326
01:47:28,719 --> 01:47:36,159
uh okay let's try that now come back to

2327
01:47:36,159 --> 01:47:36,159
the question link to the orbit space

2328
01:47:44,639 --> 01:47:47,840
one

2329
01:47:47,840 --> 01:47:51,040
or other cases either having

2330
01:47:51,040 --> 01:47:51,040
unique music or

2331
01:47:53,840 --> 01:47:58,400
can you tell me

2332
01:47:58,400 --> 01:47:59,280
why that is a different

2333
01:47:59,280 --> 01:48:02,000
so

2334
01:48:02,000 --> 01:48:02,840
i have a mathematical reason but i don't

2335
01:48:02,840 --> 01:48:05,840
have a

2336
01:48:05,840 --> 01:48:08,000
physical philosophical reason so

2337
01:48:08,000 --> 01:48:10,800
mathematically it comes and actually it

2338
01:48:10,800 --> 01:48:14,000
was uh almost proved in

2339
01:48:14,000 --> 01:48:16,960
extended accountants work

2340
01:48:16,960 --> 01:48:19,520
the the degree of freedom is

2341
01:48:19,520 --> 01:48:22,560
the the orthogonal group of dimension

2342
01:48:22,560 --> 01:48:25,920
one because it is the case where

2343
01:48:25,920 --> 01:48:26,800
the the rank of the product is only uh

2344
01:48:26,800 --> 01:48:27,679
one

2345
01:48:27,679 --> 01:48:29,360
uh

2346
01:48:29,360 --> 01:48:31,119
lower than the the red of the two

2347
01:48:31,119 --> 01:48:33,600
matrices so

2348
01:48:33,600 --> 01:48:36,320
the three parts is the orthogonal group

2349
01:48:36,320 --> 01:48:37,600
of dimension one which is only minus one

2350
01:48:37,600 --> 01:48:40,080
and one

2351
01:48:40,080 --> 01:48:42,800
the sets minus one and one and so the

2352
01:48:42,800 --> 01:48:45,440
the the set of all minimizing geodesics

2353
01:48:45,440 --> 01:48:48,560
between the points is parameterized by

2354
01:48:48,560 --> 01:48:49,920
minus one one so there are two geodesics

2355
01:48:49,920 --> 01:48:51,679
but then

2356
01:48:51,679 --> 01:48:54,960
it only comes from the structure of the

2357
01:48:54,960 --> 01:48:55,760
orthogonal group in dimension one uh

2358
01:48:55,760 --> 01:48:56,719
in

2359
01:48:56,719 --> 01:49:00,480
in

2360
01:49:00,480 --> 01:49:00,480
higher dimensions the group is

2361
01:49:12,000 --> 01:49:16,159
when you talk about the space of the

2362
01:49:16,159 --> 01:49:16,159
correlation

2363
01:49:16,320 --> 01:49:21,119
and where you look at the quotient

2364
01:49:21,119 --> 01:49:22,960
metrics

2365
01:49:22,960 --> 01:49:25,199
right after you

2366
01:49:25,199 --> 01:49:26,480
talked about you called

2367
01:49:26,480 --> 01:49:29,639
existence of

2368
01:49:29,639 --> 01:49:29,639
a logarithmic

2369
01:49:40,159 --> 01:49:42,480
unique

2370
01:49:49,980 --> 01:49:53,110
[Music]

2371
01:49:53,760 --> 01:49:57,199
or something

2372
01:49:57,199 --> 01:50:00,880
and i was not able to

2373
01:50:00,880 --> 01:50:03,040
have a clue from the experiments uh

2374
01:50:03,040 --> 01:50:03,040
i

2375
01:50:03,520 --> 01:50:07,280
sometimes i thought it it should be

2376
01:50:07,280 --> 01:50:11,840
unique and sometimes i thought it should

2377
01:50:11,840 --> 01:50:11,840
be so i i'm really lost

2378
01:50:14,639 --> 01:50:18,880
actually so

2379
01:50:18,880 --> 01:50:22,159
maybe an element that goes against the

2380
01:50:22,159 --> 01:50:22,159
uniqueness is that

2381
01:50:22,239 --> 01:50:26,239
and so probably at some places if you go

2382
01:50:26,239 --> 01:50:27,599
too far

2383
01:50:27,599 --> 01:50:28,960
uh there are

2384
01:50:28,960 --> 01:50:31,440
there must be

2385
01:50:31,440 --> 01:50:35,199
places where it is not unique

2386
01:50:35,199 --> 01:50:35,199
but i didn't i didn't include it

2387
01:50:35,440 --> 01:50:37,760
okay

2388
01:50:38,719 --> 01:50:41,840
thank you very much we now move back to

2389
01:50:41,840 --> 01:50:44,960
the

2390
01:50:44,960 --> 01:50:46,239
members who are online and uh continuing

2391
01:50:46,239 --> 01:50:48,960
with uh

2392
01:50:48,960 --> 01:50:48,960
frank nilsson

2393
01:50:50,239 --> 01:50:54,159
okay so thank you uh

2394
01:50:54,159 --> 01:50:56,400
first of all i think it's very

2395
01:50:56,400 --> 01:50:58,080
impressive and uh inspiring work that

2396
01:50:58,080 --> 01:51:00,239
you presented today

2397
01:51:00,239 --> 01:51:02,639
and uh i would like to congratulate you

2398
01:51:02,639 --> 01:51:05,119
for uh for the clarity of your

2399
01:51:05,119 --> 01:51:06,639
presentation i mean i had some hard

2400
01:51:06,639 --> 01:51:08,480
times sometimes in the manuscript

2401
01:51:08,480 --> 01:51:11,920
because it was quite uh

2402
01:51:11,920 --> 01:51:14,239
heavy reading with uh quite a heavy

2403
01:51:14,239 --> 01:51:15,280
mathematics somehow that took time for

2404
01:51:15,280 --> 01:51:17,360
me to read

2405
01:51:17,360 --> 01:51:18,719
but today your presentations show a very

2406
01:51:18,719 --> 01:51:21,679
clear picture

2407
01:51:21,679 --> 01:51:23,520
and also a a very good panorama of all

2408
01:51:23,520 --> 01:51:26,159
your work i have followed for over three

2409
01:51:26,159 --> 01:51:28,800
years so i'm very uh pleased to see

2410
01:51:28,800 --> 01:51:30,560
uh the outcome that happened in the last

2411
01:51:30,560 --> 01:51:32,960
say 18 months

2412
01:51:32,960 --> 01:51:35,360
it's it's very amazing

2413
01:51:35,360 --> 01:51:37,679
so uh this is uh

2414
01:51:37,679 --> 01:51:38,639
uh what i wanted to say first before the

2415
01:51:38,639 --> 01:51:41,040
question

2416
01:51:41,040 --> 01:51:42,639
so i i have three questions and it's a

2417
01:51:42,639 --> 01:51:43,679
high level question

2418
01:51:43,679 --> 01:51:44,880
uh

2419
01:51:44,880 --> 01:51:47,280
the first question is about the

2420
01:51:47,280 --> 01:51:49,599
motivation um

2421
01:51:49,599 --> 01:51:51,520
is if the motivation is to find a

2422
01:51:51,520 --> 01:51:53,920
dissimilarity

2423
01:51:53,920 --> 01:51:56,080
do we need to have a romanian distance

2424
01:51:56,080 --> 01:51:58,000
with a triangle inequality or not or can

2425
01:51:58,000 --> 01:51:59,920
we work at a higher level like the

2426
01:51:59,920 --> 01:52:02,560
divergence level you you talk about

2427
01:52:02,560 --> 01:52:05,360
alpha beta divergence for example and

2428
01:52:05,360 --> 01:52:07,840
once we have a divergence we can induce

2429
01:52:07,840 --> 01:52:10,560
a romanian metric and connection you

2430
01:52:10,560 --> 01:52:12,480
know like uh with a gucci methods for

2431
01:52:12,480 --> 01:52:15,199
example so

2432
01:52:15,199 --> 01:52:16,960
my question is the following when is

2433
01:52:16,960 --> 01:52:19,440
triangle inequality

2434
01:52:19,440 --> 01:52:21,920
nice in application and what when is it

2435
01:52:21,920 --> 01:52:21,920
harmful

2436
01:52:22,320 --> 01:52:26,239
um

2437
01:52:26,239 --> 01:52:27,760
i it's true that i i didn't

2438
01:52:27,760 --> 01:52:31,480
think

2439
01:52:31,480 --> 01:52:31,480
this way the

2440
01:52:32,080 --> 01:52:37,599
the necessity of remaining geometry uh i

2441
01:52:37,599 --> 01:52:39,760
i thought it more uh for the the

2442
01:52:39,760 --> 01:52:41,440
remaining metric than for the distance

2443
01:52:41,440 --> 01:52:42,560
that is induced

2444
01:52:42,560 --> 01:52:46,000
um

2445
01:52:46,000 --> 01:52:47,760
because the the the remain uh

2446
01:52:47,760 --> 01:52:50,400
from what i read i know that the

2447
01:52:50,400 --> 01:52:52,080
remaining metric helps uh

2448
01:52:52,080 --> 01:52:54,960
to do many

2449
01:52:54,960 --> 01:52:57,119
um many operations on on the manifold

2450
01:52:57,119 --> 01:52:58,480
and it seems to me that

2451
01:52:58,480 --> 01:53:00,080
the

2452
01:53:00,080 --> 01:53:02,400
the divergence

2453
01:53:02,400 --> 01:53:02,400
um

2454
01:53:02,639 --> 01:53:07,199
the the the

2455
01:53:07,199 --> 01:53:08,719
similarity measure is a bit more limited

2456
01:53:08,719 --> 01:53:11,280
but maybe i'm

2457
01:53:11,280 --> 01:53:11,280
i'm wrong

2458
01:53:11,360 --> 01:53:15,920
if you can allow me to uh to ask one

2459
01:53:15,920 --> 01:53:17,280
more tiny question in that question is

2460
01:53:17,280 --> 01:53:19,280
that for example can you try to

2461
01:53:19,280 --> 01:53:21,199
reconstruct some divergence let's say

2462
01:53:21,199 --> 01:53:22,880
for example we have f divergence which

2463
01:53:22,880 --> 01:53:25,360
are very useful for

2464
01:53:25,360 --> 01:53:28,000
uh multivariate normal distribution so

2465
01:53:28,000 --> 01:53:30,159
that the induced rimanian metric from

2466
01:53:30,159 --> 01:53:31,920
that f divergence is the metric that you

2467
01:53:31,920 --> 01:53:34,000
study

2468
01:53:34,000 --> 01:53:36,400
for example for the mixed power

2469
01:53:36,400 --> 01:53:39,119
plan you've got this alphabet

2470
01:53:39,119 --> 01:53:41,840
one realization for the other matrix can

2471
01:53:41,840 --> 01:53:44,159
can you try to go uphill and get some

2472
01:53:44,159 --> 01:53:45,679
divergences which realize the matrix for

2473
01:53:45,679 --> 01:53:49,199
example

2474
01:53:49,199 --> 01:53:52,159
um so i will uh i was only able to do it

2475
01:53:52,159 --> 01:53:54,080
for the the mixed power european metrics

2476
01:53:54,080 --> 01:53:57,119
for example i tried for the bureau of

2477
01:53:57,119 --> 01:53:58,719
research time metric but uh

2478
01:53:58,719 --> 01:54:02,320
i didn't

2479
01:54:02,320 --> 01:54:02,320
i didn't manage to so

2480
01:54:02,639 --> 01:54:06,239
but i i um

2481
01:54:06,800 --> 01:54:11,440
yeah when we have a remaining metric we

2482
01:54:11,440 --> 01:54:14,800
we have the the distance that would

2483
01:54:14,800 --> 01:54:16,320
serve as a as a dissimilarity measure

2484
01:54:16,320 --> 01:54:17,360
and so

2485
01:54:17,360 --> 01:54:20,239
the

2486
01:54:20,239 --> 01:54:20,239
your question is

2487
01:54:21,360 --> 01:54:24,320
what do we

2488
01:54:24,320 --> 01:54:25,119
what do we

2489
01:54:25,119 --> 01:54:27,760
have

2490
01:54:27,760 --> 01:54:29,599
uh on distances that we don't have with

2491
01:54:29,599 --> 01:54:30,480
divergences

2492
01:54:30,480 --> 01:54:32,639
uh

2493
01:54:32,639 --> 01:54:35,360
for example if we take the future our

2494
01:54:35,360 --> 01:54:37,440
distance of multivariate normals we we

2495
01:54:37,440 --> 01:54:40,560
don't have uh we don't have the distance

2496
01:54:40,560 --> 01:54:42,320
right we we have a judaistic equation

2497
01:54:42,320 --> 01:54:43,199
but we don't have class form familiar

2498
01:54:43,199 --> 01:54:44,159
right

2499
01:54:44,159 --> 01:54:46,800
with the

2500
01:54:46,800 --> 01:54:48,159
with the with the mean with the nominal

2501
01:54:48,159 --> 01:54:48,840
mean you mean

2502
01:54:48,840 --> 01:54:50,719
okay

2503
01:54:50,719 --> 01:54:53,040
yeah sorry so it's a bit uh different

2504
01:54:53,040 --> 01:54:55,199
yeah yeah you're right but no no but

2505
01:54:55,199 --> 01:54:57,119
well it's true that we we have the the

2506
01:54:57,119 --> 01:55:00,080
formula for the

2507
01:55:00,080 --> 01:55:01,360
for for uh fischer row metric on spd

2508
01:55:01,360 --> 01:55:02,400
matrices

2509
01:55:02,400 --> 01:55:05,440
but then

2510
01:55:05,440 --> 01:55:09,199
no to be honest i i i don't know exactly

2511
01:55:09,199 --> 01:55:10,000
what the i think the triangle inequality

2512
01:55:10,000 --> 01:55:13,440
uh

2513
01:55:13,440 --> 01:55:14,239
is useful in in some uh algorithms but

2514
01:55:14,239 --> 01:55:15,520
uh

2515
01:55:15,520 --> 01:55:16,960
i don't know

2516
01:55:16,960 --> 01:55:17,520
uh more

2517
01:55:17,520 --> 01:55:19,360
for about

2518
01:55:19,360 --> 01:55:21,360
neighbor queries you can prune for

2519
01:55:21,360 --> 01:55:23,679
example for pruning technique in data

2520
01:55:23,679 --> 01:55:24,880
structures or query location maybe it's

2521
01:55:24,880 --> 01:55:27,040
useful but

2522
01:55:27,040 --> 01:55:29,360
yeah it's just one high-level question i

2523
01:55:29,360 --> 01:55:31,280
wanted to ask

2524
01:55:31,280 --> 01:55:33,280
the second one is also very high level

2525
01:55:33,280 --> 01:55:35,440
uh you mentioned precision matrix and

2526
01:55:35,440 --> 01:55:37,040
covariance matrix and actually the space

2527
01:55:37,040 --> 01:55:39,520
that you study is uh

2528
01:55:39,520 --> 01:55:41,679
is semi-positive definite matrices right

2529
01:55:41,679 --> 01:55:44,080
so on the open con you you have a

2530
01:55:44,080 --> 01:55:46,400
perfect uh projection and and the

2531
01:55:46,400 --> 01:55:48,800
parametrization are

2532
01:55:48,800 --> 01:55:51,440
interchangeable but

2533
01:55:51,440 --> 01:55:53,840
if you take precision matrices uh with

2534
01:55:53,840 --> 01:55:56,639
rank deficient then it is uh covalent

2535
01:55:56,639 --> 01:55:59,119
matrices with infin infinite eigenvalues

2536
01:55:59,119 --> 01:56:00,400
so my question is that do you do you

2537
01:56:00,400 --> 01:56:02,320
think it's a good question to look at

2538
01:56:02,320 --> 01:56:03,199
the compactification

2539
01:56:03,199 --> 01:56:04,239
uh

2540
01:56:04,239 --> 01:56:05,440
of

2541
01:56:05,440 --> 01:56:07,920
of the cone

2542
01:56:07,920 --> 01:56:10,970
uh well you can both have a zero and

2543
01:56:10,970 --> 01:56:12,840
infinite eigenvalues somehow

2544
01:56:12,840 --> 01:56:16,000
[Music]

2545
01:56:16,000 --> 01:56:17,040
yay it should be a very very nice idea i

2546
01:56:17,040 --> 01:56:18,960
didn't

2547
01:56:18,960 --> 01:56:21,440
yeah i didn't really think about it

2548
01:56:21,440 --> 01:56:22,140
before but i think that

2549
01:56:22,140 --> 01:56:24,000
um

2550
01:56:24,000 --> 01:56:25,599
[Music]

2551
01:56:25,599 --> 01:56:26,639
um

2552
01:56:26,639 --> 01:56:29,199
the

2553
01:56:29,199 --> 01:56:31,679
sorry um

2554
01:56:31,679 --> 01:56:33,920
when we take the the tuba inverse of uh

2555
01:56:33,920 --> 01:56:34,719
of the covariance matrix

2556
01:56:34,719 --> 01:56:37,599
uh

2557
01:56:37,599 --> 01:56:40,159
we keep the the

2558
01:56:40,159 --> 01:56:43,679
the new eigenvalues to zero

2559
01:56:43,679 --> 01:56:46,800
and so we we could also study the the

2560
01:56:46,800 --> 01:56:49,119
space with with a zero and with the

2561
01:56:49,119 --> 01:56:51,040
not infinity

2562
01:56:51,040 --> 01:56:52,719
instead of zero

2563
01:56:52,719 --> 01:56:54,840
so

2564
01:56:54,840 --> 01:56:56,560
you mean if we compactify

2565
01:56:56,560 --> 01:56:59,040
we

2566
01:56:59,040 --> 01:57:01,280
we can actually invert in this

2567
01:57:01,280 --> 01:57:02,800
compactified code and so then yeah and

2568
01:57:02,800 --> 01:57:05,679
so it's kind of having a double code

2569
01:57:05,679 --> 01:57:08,320
model one code for the decision and one

2570
01:57:08,320 --> 01:57:10,639
for the covariance yeah i remember that

2571
01:57:10,639 --> 01:57:12,800
you you sent it to me yeah i think this

2572
01:57:12,800 --> 01:57:15,199
is very intriguing very it's very

2573
01:57:15,199 --> 01:57:15,199
intriguing

2574
01:57:15,280 --> 01:57:19,840
and it's related to parameterization

2575
01:57:19,840 --> 01:57:21,679
yeah

2576
01:57:21,679 --> 01:57:25,280
when you map the eigenvalue into

2577
01:57:25,280 --> 01:57:25,280
interval zero one

2578
01:57:26,880 --> 01:57:32,400
yeah i don't remember exactly how it's

2579
01:57:32,400 --> 01:57:36,159
built this uh double cone

2580
01:57:36,159 --> 01:57:38,320
but yeah probably the the the geometry

2581
01:57:38,320 --> 01:57:41,280
is completely different for example i i

2582
01:57:41,280 --> 01:57:43,280
don't know how uh to generalize the

2583
01:57:43,280 --> 01:57:45,119
affining variant metric to the to the

2584
01:57:45,119 --> 01:57:48,000
compact uh

2585
01:57:48,000 --> 01:57:50,000
to the compactify the cone and then i

2586
01:57:50,000 --> 01:57:52,159
guess the the geometries on the double

2587
01:57:52,159 --> 01:57:55,199
corners are completely different from

2588
01:57:55,199 --> 01:57:58,080
the ones i i mentioned today

2589
01:57:58,080 --> 01:57:58,080
on monday

2590
01:57:58,880 --> 01:58:02,560
i mean

2591
01:58:02,560 --> 01:58:05,760
the paper i'm i'm referring is a

2592
01:58:05,760 --> 01:58:06,880
wallpaper by james and very not so much

2593
01:58:06,880 --> 01:58:08,960
none and

2594
01:58:08,960 --> 01:58:11,199
but i think there's a lot of perspective

2595
01:58:11,199 --> 01:58:14,719
in that paper actually

2596
01:58:14,719 --> 01:58:17,280
but um okay so my last question uh

2597
01:58:17,280 --> 01:58:20,719
is more about application because

2598
01:58:20,719 --> 01:58:22,800
so you have metrics on uh spd uh spd con

2599
01:58:22,800 --> 01:58:24,080
you have matrix on on

2600
01:58:24,080 --> 01:58:25,679
the laptop

2601
01:58:25,679 --> 01:58:27,760
and for example you

2602
01:58:27,760 --> 01:58:28,880
you you could try to look at interplay

2603
01:58:28,880 --> 01:58:30,400
of uh

2604
01:58:30,400 --> 01:58:32,719
of the con with the lift up by

2605
01:58:32,719 --> 01:58:33,520
projection like from finding the closest

2606
01:58:33,520 --> 01:58:35,440
uh

2607
01:58:35,440 --> 01:58:36,239
correlation matrix for example and to

2608
01:58:36,239 --> 01:58:38,320
look

2609
01:58:38,320 --> 01:58:40,080
at all those uh

2610
01:58:40,080 --> 01:58:42,080
all those romanian metrics for defining

2611
01:58:42,080 --> 01:58:45,360
projection and have properties for

2612
01:58:45,360 --> 01:58:46,800
example did you try to look at that or

2613
01:58:46,800 --> 01:58:48,960
so not yet

2614
01:58:48,960 --> 01:58:52,480
i i didn't i didn't try to actually i

2615
01:58:52,480 --> 01:58:54,960
didn't try because uh i for so far i

2616
01:58:54,960 --> 01:58:58,080
only studied the the space of full rank

2617
01:58:58,080 --> 01:59:00,639
correlation matrices and i saw that in

2618
01:59:00,639 --> 01:59:02,719
these applications where we need the

2619
01:59:02,719 --> 01:59:05,520
projection of course the projection

2620
01:59:05,520 --> 01:59:08,880
belongs to the boundary and so there is

2621
01:59:08,880 --> 01:59:12,320
a new eigenvalue so but probably for the

2622
01:59:12,320 --> 01:59:12,320
the next steps of uh

2623
01:59:12,639 --> 01:59:19,280
when we when we study a geometry

2624
01:59:19,280 --> 01:59:21,280
for the whole ellipto the closed electro

2625
01:59:21,280 --> 01:59:22,400
then it should be very interesting to

2626
01:59:22,400 --> 01:59:24,239
look at these

2627
01:59:24,239 --> 01:59:26,000
applications

2628
01:59:26,000 --> 01:59:28,400
okay i have many questions but i will

2629
01:59:28,400 --> 01:59:30,639
stop here for uh for

2630
01:59:30,639 --> 01:59:32,560
for the sake of time

2631
01:59:32,560 --> 01:59:34,639
but uh congratulations again for for

2632
01:59:34,639 --> 01:59:35,760
this very interesting piece of work

2633
01:59:35,760 --> 01:59:37,119
thank you

2634
01:59:37,119 --> 01:59:41,040
thank you very much

2635
01:59:41,040 --> 01:59:41,040
alright so we moved to min

2636
01:59:43,679 --> 01:59:47,920
uh um you very much for the spectacular

2637
01:59:47,920 --> 01:59:50,800
presentation and and congratulations on

2638
01:59:50,800 --> 01:59:52,960
the great and the great thesis um it's a

2639
01:59:52,960 --> 01:59:54,880
lot of amazing the exams really amazing

2640
01:59:54,880 --> 01:59:56,400
and it's a it's a great body of work so

2641
01:59:56,400 --> 01:59:58,880
congratulations

2642
01:59:58,880 --> 02:00:02,480
so i have uh accepted some questions

2643
02:00:02,480 --> 02:00:04,000
what did he ask my other my other uh um

2644
02:00:04,000 --> 02:00:05,840
members actually so i would give my

2645
02:00:05,840 --> 02:00:08,560
questions actually quite short

2646
02:00:08,560 --> 02:00:10,000
so there was this question about this so

2647
02:00:10,000 --> 02:00:12,480
you have like you're kind of trying to

2648
02:00:12,480 --> 02:00:14,320
have a unification of kind of of the

2649
02:00:14,320 --> 02:00:15,599
different matrix right and then suddenly

2650
02:00:15,599 --> 02:00:17,599
we have like you know starting from a

2651
02:00:17,599 --> 02:00:20,880
couple of years ago this is like loud mr

2652
02:00:20,880 --> 02:00:22,960
scholaski a luxury magic and so on so do

2653
02:00:22,960 --> 02:00:26,080
you think like these can fit into

2654
02:00:26,080 --> 02:00:27,440
somehow like a bigger framework

2655
02:00:27,440 --> 02:00:28,800
fitting what

2656
02:00:28,800 --> 02:00:30,000
the this is like a bigger framework

2657
02:00:30,000 --> 02:00:31,760
because you have like this kind of like

2658
02:00:31,760 --> 02:00:33,920
it's a mixed power between like do you

2659
02:00:33,920 --> 02:00:37,040
think there's a you cannot kind of fit

2660
02:00:37,040 --> 02:00:39,520
this like a lock scholastic

2661
02:00:39,520 --> 02:00:41,360
kind of matrix into into the make bigger

2662
02:00:41,360 --> 02:00:44,239
framework

2663
02:00:44,239 --> 02:00:45,599
yes i remember our discussions on uh

2664
02:00:45,599 --> 02:00:48,400
finding the

2665
02:00:48,400 --> 02:00:50,320
the the global frameworks for uh

2666
02:00:50,320 --> 02:00:52,159
integrating the

2667
02:00:52,159 --> 02:00:53,520
the your uh

2668
02:00:53,520 --> 02:00:57,599
alpha proper

2669
02:00:57,599 --> 02:00:58,880
and and our mixed power european etc um

2670
02:00:58,880 --> 02:00:59,760
so

2671
02:00:59,760 --> 02:01:02,560
i

2672
02:01:02,560 --> 02:01:05,840
yeah something i didn't mention is so

2673
02:01:05,840 --> 02:01:08,400
first uh i i think what what's

2674
02:01:08,400 --> 02:01:10,880
what's interesting is to find the the

2675
02:01:10,880 --> 02:01:14,080
smallest family uh with the

2676
02:01:14,080 --> 02:01:14,960
with the with few parameters such that

2677
02:01:14,960 --> 02:01:16,400
uh

2678
02:01:16,400 --> 02:01:19,119
there is some

2679
02:01:19,119 --> 02:01:21,440
there are some principles behind the the

2680
02:01:21,440 --> 02:01:23,760
metrics that are in the family and

2681
02:01:23,760 --> 02:01:25,280
actually uh

2682
02:01:25,280 --> 02:01:28,239
if we take the

2683
02:01:28,239 --> 02:01:30,080
the co-metrics of the mixed power

2684
02:01:30,080 --> 02:01:33,280
euclidean matrix

2685
02:01:33,280 --> 02:01:36,400
this includes the yoga suction metric uh

2686
02:01:36,400 --> 02:01:38,639
because you know the the coefficient uh

2687
02:01:38,639 --> 02:01:42,800
in the bureau search time metric is uh

2688
02:01:42,800 --> 02:01:45,199
one over d i plus d j if you write it uh

2689
02:01:45,199 --> 02:01:48,000
uh in in

2690
02:01:48,000 --> 02:01:48,800
diagonal at a diagonal point

2691
02:01:48,800 --> 02:01:51,599
uh

2692
02:01:51,599 --> 02:01:54,080
and if you if you inverse this uh you

2693
02:01:54,080 --> 02:01:56,800
you get a metric that is in the family

2694
02:01:56,800 --> 02:01:59,199
of uh mixed power euclidean matrix so

2695
02:01:59,199 --> 02:02:01,760
actually if we take the the the mixed

2696
02:02:01,760 --> 02:02:03,040
power euclidean metrics and their

2697
02:02:03,040 --> 02:02:06,480
commetrics

2698
02:02:06,480 --> 02:02:09,280
uh here we can we can include the the

2699
02:02:09,280 --> 02:02:11,040
view of social media so

2700
02:02:11,040 --> 02:02:13,440
then uh

2701
02:02:13,440 --> 02:02:16,239
that's why also at some point i stopped

2702
02:02:16,239 --> 02:02:19,040
to find to try to find some some

2703
02:02:19,040 --> 02:02:20,960
families limited metrics that

2704
02:02:20,960 --> 02:02:22,000
because i i

2705
02:02:22,000 --> 02:02:24,400
i thought

2706
02:02:24,400 --> 02:02:25,920
it was enough uh with this this

2707
02:02:25,920 --> 02:02:27,920
classification

2708
02:02:27,920 --> 02:02:31,440
um

2709
02:02:31,440 --> 02:02:31,440
these scholastic metrics they

2710
02:02:31,520 --> 02:02:36,880
they they are they are not in variants

2711
02:02:36,880 --> 02:02:38,480
under anything i tried at least

2712
02:02:38,480 --> 02:02:40,480
and so

2713
02:02:40,480 --> 02:02:41,280
i i don't know exactly how they could

2714
02:02:41,280 --> 02:02:42,560
fit

2715
02:02:42,560 --> 02:02:44,960
in

2716
02:02:44,960 --> 02:02:48,400
in a bigger framework but

2717
02:02:48,400 --> 02:02:52,000
i'm not sure we we need it actually

2718
02:02:52,000 --> 02:02:52,000
that's why i didn't investigate

2719
02:02:52,159 --> 02:02:55,840
i think it could be i think it could be

2720
02:02:55,840 --> 02:02:58,000
interesting from a numerical viewpoint

2721
02:02:58,000 --> 02:02:59,840
to see how they work actually because i

2722
02:02:59,840 --> 02:03:02,000
think because the competition is kind of

2723
02:03:02,000 --> 02:03:04,000
like it's kind of numerically stable

2724
02:03:04,000 --> 02:03:06,560
right so i i'm kind of curious actually

2725
02:03:06,560 --> 02:03:07,920
how they work on on on vr data actually

2726
02:03:07,920 --> 02:03:09,840
because it's also

2727
02:03:09,840 --> 02:03:11,280
it's i mean it's also kind of uh it's

2728
02:03:11,280 --> 02:03:13,840
actually kind of

2729
02:03:13,840 --> 02:03:15,920
interesting somehow it's a

2730
02:03:15,920 --> 02:03:19,679
mathematical viewpoint is interesting

2731
02:03:19,679 --> 02:03:22,239
and it's a yeah no i'm sorry uh

2732
02:03:22,239 --> 02:03:25,679
something i didn't try but uh

2733
02:03:25,679 --> 02:03:29,679
i think that between the the

2734
02:03:29,679 --> 02:03:32,400
variance uh the various scholeski

2735
02:03:32,400 --> 02:03:35,119
based metrics on spd matrices probably

2736
02:03:35,119 --> 02:03:37,520
if we do a similar operation then what

2737
02:03:37,520 --> 02:03:39,199
we did on the procrust or

2738
02:03:39,199 --> 02:03:40,480
finding variant if we

2739
02:03:40,480 --> 02:03:42,400
if we

2740
02:03:42,400 --> 02:03:45,199
if we put the power transformation

2741
02:03:45,199 --> 02:03:47,599
of the of the scholeski metric probably

2742
02:03:47,599 --> 02:03:49,280
we could reach the low scholastic metric

2743
02:03:49,280 --> 02:03:50,880
or yeah yeah yeah you know i think we

2744
02:03:50,880 --> 02:03:52,719
can do that yeah we can we can do like

2745
02:03:52,719 --> 02:03:54,320
the power yeah and then we

2746
02:03:54,320 --> 02:03:55,679
uh we can reach it because actually it's

2747
02:03:55,679 --> 02:03:57,679
actually well defined because it's

2748
02:03:57,679 --> 02:04:00,079
because all the eigenvalues

2749
02:04:00,079 --> 02:04:02,320
all the all the diagonal elements are

2750
02:04:02,320 --> 02:04:04,400
positive yeah and the the space of

2751
02:04:04,400 --> 02:04:06,719
triangular matrices is stable by all

2752
02:04:06,719 --> 02:04:09,599
these power operations so probably we

2753
02:04:09,599 --> 02:04:09,599
could do this but

2754
02:04:09,760 --> 02:04:13,199
so the the second one is more like a

2755
02:04:13,199 --> 02:04:14,800
command actually because we actually we

2756
02:04:14,800 --> 02:04:16,960
like in machine learning we actually

2757
02:04:16,960 --> 02:04:18,320
care a lot about like um defining like a

2758
02:04:18,320 --> 02:04:20,719
positive definite gun especially

2759
02:04:20,719 --> 02:04:22,719
gaussian kernel with these uh distances

2760
02:04:22,719 --> 02:04:24,239
and so for example like we have the fi

2761
02:04:24,239 --> 02:04:25,360
invariant metric has more kind of

2762
02:04:25,360 --> 02:04:26,800
invariant actually has the more

2763
02:04:26,800 --> 02:04:28,400
invariant properties and then with a lot

2764
02:04:28,400 --> 02:04:31,119
of cleaning matrix for example has like

2765
02:04:31,119 --> 02:04:33,040
fewer invert so they still have no like

2766
02:04:33,040 --> 02:04:35,280
fewer invariant properties but then

2767
02:04:35,280 --> 02:04:38,960
because it's flat and so so we can

2768
02:04:38,960 --> 02:04:38,960
define a gaussian donor on using

2769
02:04:40,480 --> 02:04:44,239
um and so it's it's also like one

2770
02:04:44,239 --> 02:04:46,480
consideration as well so not just like

2771
02:04:46,480 --> 02:04:48,800
not not only kind of the uh the

2772
02:04:48,800 --> 02:04:51,119
invariant properties actually so we also

2773
02:04:51,119 --> 02:04:52,960
care about uh these things so defining

2774
02:04:52,960 --> 02:04:53,679
the possibility of physically using it

2775
02:04:53,679 --> 02:04:55,360
so

2776
02:04:55,360 --> 02:04:56,800
so this kind of also like kind of kind

2777
02:04:56,800 --> 02:04:59,280
of related to the questions that pierre

2778
02:04:59,280 --> 02:05:00,560
and bran was asking uh before so kind of

2779
02:05:00,560 --> 02:05:02,719
so another thing from the machine

2780
02:05:02,719 --> 02:05:04,560
learning viewpoint is actually possible

2781
02:05:04,560 --> 02:05:07,119
we would like to define a gaussian gonna

2782
02:05:07,119 --> 02:05:09,520
using using these distances you mean

2783
02:05:09,520 --> 02:05:12,159
that it could be another principle to

2784
02:05:12,159 --> 02:05:13,920
find interesting uh i i think it could

2785
02:05:13,920 --> 02:05:15,760
be uh i think it could be

2786
02:05:15,760 --> 02:05:16,960
like it could be useful to point out

2787
02:05:16,960 --> 02:05:18,719
like because actually for people in

2788
02:05:18,719 --> 02:05:20,320
machine learning we like kind of if you

2789
02:05:20,320 --> 02:05:22,239
look at the various application in

2790
02:05:22,239 --> 02:05:23,840
machine learning actually uh

2791
02:05:23,840 --> 02:05:25,280
people actually use the log equilibrium

2792
02:05:25,280 --> 02:05:27,199
uh major because actually before they

2793
02:05:27,199 --> 02:05:28,800
apply very much like we use the distance

2794
02:05:28,800 --> 02:05:30,400
right that's just only the distance but

2795
02:05:30,400 --> 02:05:31,840
then for the alloy clear image you can

2796
02:05:31,840 --> 02:05:32,880
you can you can you find a gunner as

2797
02:05:32,880 --> 02:05:34,960
well so you can have like kind of

2798
02:05:34,960 --> 02:05:37,040
methods so it's it's another actually

2799
02:05:37,040 --> 02:05:38,480
feature it's not another feature that

2800
02:05:38,480 --> 02:05:39,920
actually is useful for people in machine

2801
02:05:39,920 --> 02:05:41,360
learning computer vision that we we

2802
02:05:41,360 --> 02:05:44,000
actually use

2803
02:05:44,000 --> 02:05:46,719
we actually use it actually um

2804
02:05:46,719 --> 02:05:48,719
so now so another question is on the

2805
02:05:48,719 --> 02:05:50,239
left this log scaling with the log

2806
02:05:50,239 --> 02:05:52,079
scaling um

2807
02:05:52,079 --> 02:05:53,920
metric with the uh

2808
02:05:53,920 --> 02:05:56,880
uh with for the correlation matrix so

2809
02:05:56,880 --> 02:05:58,639
it's uh is is this log operation um

2810
02:05:58,639 --> 02:06:00,840
explicit is it like is it does it have a

2811
02:06:00,840 --> 02:06:03,920
closed form formula is it

2812
02:06:03,920 --> 02:06:06,239
explicit one direction yes in the other

2813
02:06:06,239 --> 02:06:08,400
direction not so

2814
02:06:08,400 --> 02:06:10,880
if you take this row zero space the

2815
02:06:10,880 --> 02:06:12,239
space of symmetric matrices uh with new

2816
02:06:12,239 --> 02:06:14,719
row sums

2817
02:06:14,719 --> 02:06:17,360
uh if you take the exponential of this

2818
02:06:17,360 --> 02:06:18,400
matrix and then the correlation

2819
02:06:18,400 --> 02:06:20,079
if you

2820
02:06:20,079 --> 02:06:22,480
normalized by the diagonal you get a

2821
02:06:22,480 --> 02:06:24,560
correlation matrix and this is a

2822
02:06:24,560 --> 02:06:27,360
bijective parameterization

2823
02:06:27,360 --> 02:06:29,679
of correlation matrices uh

2824
02:06:29,679 --> 02:06:31,760
uh yes that i didn't mention that the

2825
02:06:31,760 --> 02:06:33,599
converse operation the the inverse

2826
02:06:33,599 --> 02:06:36,239
different morphism is not

2827
02:06:36,239 --> 02:06:38,719
known in interest form but actually we

2828
02:06:38,719 --> 02:06:40,960
can compute it very efficiently in in

2829
02:06:40,960 --> 02:06:43,360
dimensions like i propose

2830
02:06:43,360 --> 02:06:44,480
an algorithm because i wanted to have at

2831
02:06:44,480 --> 02:06:46,159
least one

2832
02:06:46,159 --> 02:06:49,360
but actually i think there are other

2833
02:06:49,360 --> 02:06:50,719
methods to to to compute these scaling

2834
02:06:50,719 --> 02:06:53,360
that is

2835
02:06:53,360 --> 02:06:54,960
there is a huge uh literature on this

2836
02:06:54,960 --> 02:06:57,440
scaling operation

2837
02:06:57,440 --> 02:06:59,440
and so i found some references where

2838
02:06:59,440 --> 02:07:02,480
they know how to compute the scaling

2839
02:07:02,480 --> 02:07:04,400
very efficiently

2840
02:07:04,400 --> 02:07:06,719
and so so you can you can define also

2841
02:07:06,719 --> 02:07:09,360
like a positive definition using this

2842
02:07:09,360 --> 02:07:11,119
distance right with this uh

2843
02:07:11,119 --> 02:07:12,719
okay good so

2844
02:07:12,719 --> 02:07:14,560
for the further level gradient metric

2845
02:07:14,560 --> 02:07:16,480
actually put for the spd image it turns

2846
02:07:16,480 --> 02:07:18,480
the spd images into like a set of speed

2847
02:07:18,480 --> 02:07:20,159
images into a vector space not just like

2848
02:07:20,159 --> 02:07:22,079
uh i just it's like a vector space so

2849
02:07:22,079 --> 02:07:23,760
like it turns into an inner product

2850
02:07:23,760 --> 02:07:24,960
space actually so this is the same thing

2851
02:07:24,960 --> 02:07:27,280
with this with this log scaling

2852
02:07:27,280 --> 02:07:28,639
operation for the correlation

2853
02:07:28,639 --> 02:07:31,040
yeah that's why

2854
02:07:31,040 --> 02:07:32,719
yeah that's why i call them all

2855
02:07:32,719 --> 02:07:35,440
logically in metrics okay because

2856
02:07:35,440 --> 02:07:37,679
actually they all go bijectively to

2857
02:07:37,679 --> 02:07:40,560
vector space and so you can define all

2858
02:07:40,560 --> 02:07:42,079
the kernels you want

2859
02:07:42,079 --> 02:07:44,400
no it's true that i didn't think about

2860
02:07:44,400 --> 02:07:45,280
this application

2861
02:07:45,280 --> 02:07:47,119
okay

2862
02:07:47,119 --> 02:07:49,440
so uh another question is actually

2863
02:07:49,440 --> 02:07:52,159
related to what professor was asking is

2864
02:07:52,159 --> 02:07:54,000
what the television means very center so

2865
02:07:54,000 --> 02:07:55,360
for the for the rest of us time distance

2866
02:07:55,360 --> 02:07:56,719
actually so this is defined for

2867
02:07:56,719 --> 02:07:59,599
everything for like all the governance

2868
02:07:59,599 --> 02:08:01,520
which is singular or not right but

2869
02:08:01,520 --> 02:08:03,520
but currently we have a very center

2870
02:08:03,520 --> 02:08:05,679
equation the kind of six point equation

2871
02:08:05,679 --> 02:08:07,679
for pairing the very center of the with

2872
02:08:07,679 --> 02:08:10,079
the uh the resources time distance but

2873
02:08:10,079 --> 02:08:11,520
this equation is actually not valid for

2874
02:08:11,520 --> 02:08:12,400
the singular setting because it's

2875
02:08:12,400 --> 02:08:14,320
actually

2876
02:08:14,320 --> 02:08:15,599
because uh uh so it's actually this

2877
02:08:15,599 --> 02:08:17,440
equation it has been known for a very

2878
02:08:17,440 --> 02:08:20,159
long time but it's actually it's

2879
02:08:20,159 --> 02:08:22,560
it's actually it speaks to the case when

2880
02:08:22,560 --> 02:08:24,239
for the non-singular setting because um

2881
02:08:24,239 --> 02:08:26,480
the proof of existence and uniqueness

2882
02:08:26,480 --> 02:08:28,239
depends on the fact that

2883
02:08:28,239 --> 02:08:30,639
everything is actually strictly positive

2884
02:08:30,639 --> 02:08:32,960
and also the fixed point equation itself

2885
02:08:32,960 --> 02:08:34,880
it actually has infinitely many

2886
02:08:34,880 --> 02:08:37,040
singular fixed points so it's actually

2887
02:08:37,040 --> 02:08:38,719
not not it's actually cannot be used in

2888
02:08:38,719 --> 02:08:40,800
the case when the very center for

2889
02:08:40,800 --> 02:08:42,719
example is singular so i'm bad question

2890
02:08:42,719 --> 02:08:44,560
for the um

2891
02:08:44,560 --> 02:08:45,599
like how much it actually uh

2892
02:08:45,599 --> 02:08:48,000
whether you have kind of thought about

2893
02:08:48,000 --> 02:08:49,520
this very central uh

2894
02:08:49,520 --> 02:08:50,960
version of very central much especially

2895
02:08:50,960 --> 02:08:53,119
for you know in the case of the the

2896
02:08:53,119 --> 02:08:54,800
british as a stein uh distance you have

2897
02:08:54,800 --> 02:08:56,480
like a you have a like a one whole

2898
02:08:56,480 --> 02:08:58,560
chapter in the end with the coverage

2899
02:08:58,560 --> 02:09:00,000
countries of a um uh different ranks and

2900
02:09:00,000 --> 02:09:01,360
so on i'm not questioning whether you

2901
02:09:01,360 --> 02:09:04,599
have actually thought about

2902
02:09:04,599 --> 02:09:04,599
these questions

2903
02:09:10,639 --> 02:09:13,920
oh because because it actually has for

2904
02:09:13,920 --> 02:09:14,960
example it has zero as the fixed point

2905
02:09:14,960 --> 02:09:17,119
it has actually it can be shown that it

2906
02:09:17,119 --> 02:09:19,840
has infinitely many singular fixed

2907
02:09:19,840 --> 02:09:21,840
points uh fake points so i think which

2908
02:09:21,840 --> 02:09:24,000
are singular matrixes and so it's not

2909
02:09:24,000 --> 02:09:25,599
suitable for for finding the fixed point

2910
02:09:25,599 --> 02:09:27,760
when they're fixing if if the very

2911
02:09:27,760 --> 02:09:30,079
center for example is

2912
02:09:30,079 --> 02:09:32,960
it's singular then it's actually it's

2913
02:09:32,960 --> 02:09:35,360
not it's not suitable okay maybe i will

2914
02:09:35,360 --> 02:09:36,800
talk to you maybe after this

2915
02:09:36,800 --> 02:09:37,760
i can discuss with you after this in

2916
02:09:37,760 --> 02:09:39,520
detail

2917
02:09:39,520 --> 02:09:41,840
when you say barycentric equation you

2918
02:09:41,840 --> 02:09:43,920
mean the the the

2919
02:09:43,920 --> 02:09:46,320
equation to to find the the very center

2920
02:09:46,320 --> 02:09:49,360
or the the fresher mean of uh yeah yeah

2921
02:09:49,360 --> 02:09:51,440
yeah set of matrices i think that uh in

2922
02:09:51,440 --> 02:09:54,159
in each stratum so if you know the rank

2923
02:09:54,159 --> 02:09:56,000
of your matrices i guess that the the

2924
02:09:56,000 --> 02:09:58,239
equation should be well posed because

2925
02:09:58,239 --> 02:10:01,199
you are uh

2926
02:10:01,199 --> 02:10:03,679
in a manifold so actually you

2927
02:10:03,679 --> 02:10:06,560
it's it's from the work of estelle and

2928
02:10:06,560 --> 02:10:09,119
here antoine when you

2929
02:10:09,119 --> 02:10:10,320
when you are in this manifold uh you can

2930
02:10:10,320 --> 02:10:12,400
lift

2931
02:10:12,400 --> 02:10:15,450
everything in

2932
02:10:15,450 --> 02:10:16,800
i don't see where the the singularity uh

2933
02:10:16,800 --> 02:10:18,560
[Music]

2934
02:10:18,560 --> 02:10:21,760
would uh make a

2935
02:10:21,760 --> 02:10:24,320
problem then it's true that between

2936
02:10:24,320 --> 02:10:27,199
matrices of different ranks they're

2937
02:10:27,199 --> 02:10:27,199
yeah they're going

2938
02:10:33,150 --> 02:10:36,240
[Music]

2939
02:10:36,400 --> 02:10:39,840
my one final comment is actually on on

2940
02:10:39,840 --> 02:10:42,079
on

2941
02:10:42,079 --> 02:10:43,840
chapter eight i think it's uh i think

2942
02:10:43,840 --> 02:10:45,599
the presentation could be improved

2943
02:10:45,599 --> 02:10:47,520
because uh

2944
02:10:47,520 --> 02:10:49,440
coming to a as i agree with what pierre

2945
02:10:49,440 --> 02:10:50,960
was writing up on his report that it's a

2946
02:10:50,960 --> 02:10:52,400
bit confusing because when you were

2947
02:10:52,400 --> 02:10:53,440
talking about the different conjectures

2948
02:10:53,440 --> 02:10:55,440
and so on

2949
02:10:55,440 --> 02:10:57,119
uh it was a bit confusing to read but

2950
02:10:57,119 --> 02:10:58,800
otherwise like the whole

2951
02:10:58,800 --> 02:11:00,719
physics is great it's like the results

2952
02:11:00,719 --> 02:11:02,079
are really great and

2953
02:11:02,079 --> 02:11:04,079
thank you for the presentation and all

2954
02:11:04,079 --> 02:11:05,520
the results thanks a lot

2955
02:11:05,520 --> 02:11:07,360
also for me

2956
02:11:07,360 --> 02:11:08,560
thank you

2957
02:11:08,560 --> 02:11:11,199
um

2958
02:11:11,199 --> 02:11:12,239
so it it comes to me i think that um

2959
02:11:12,239 --> 02:11:15,119
we've been

2960
02:11:15,119 --> 02:11:16,079
asking you questions already for

2961
02:11:16,079 --> 02:11:18,560
um

2962
02:11:18,560 --> 02:11:21,920
90 minutes so

2963
02:11:21,920 --> 02:11:24,079
i i i cut down my time i just asked one

2964
02:11:24,079 --> 02:11:25,920
maybe very naive question but as an

2965
02:11:25,920 --> 02:11:27,760
engineer i like to think of positive

2966
02:11:27,760 --> 02:11:29,440
definite matrices as

2967
02:11:29,440 --> 02:11:32,079
you know matrix generalizations of

2968
02:11:32,079 --> 02:11:34,800
positive numbers and i tend to associate

2969
02:11:34,800 --> 02:11:35,679
those two hyperbolic geometries and

2970
02:11:35,679 --> 02:11:37,760
so

2971
02:11:37,760 --> 02:11:39,440
when you discuss that many of those

2972
02:11:39,440 --> 02:11:42,880
metrics have

2973
02:11:42,880 --> 02:11:45,440
you know not not not constant sign

2974
02:11:45,440 --> 02:11:45,440
curvature

2975
02:11:46,480 --> 02:11:51,599
i'd like to ask is this a sufficient

2976
02:11:51,599 --> 02:11:53,280
criterion to reject those metrics are i

2977
02:11:53,280 --> 02:11:54,639
mean could we could could we consider

2978
02:11:54,639 --> 02:11:56,800
this as sort of

2979
02:11:56,800 --> 02:11:59,119
artifacts of the construction

2980
02:11:59,119 --> 02:12:01,360
or would you defend that sometimes it

2981
02:12:01,360 --> 02:12:02,400
makes sense to have

2982
02:12:02,400 --> 02:12:04,880
such

2983
02:12:04,880 --> 02:12:06,480
sort of complex curvatures and and

2984
02:12:06,480 --> 02:12:08,639
in that case would there be any

2985
02:12:08,639 --> 02:12:10,380
interpretation

2986
02:12:10,380 --> 02:12:12,239
i think that's not a naive question

2987
02:12:12,239 --> 02:12:14,079
[Laughter]

2988
02:12:14,079 --> 02:12:16,639
uh no no

2989
02:12:16,639 --> 02:12:18,639
on the contrary it's quite a

2990
02:12:18,639 --> 02:12:20,480
deep question that i i

2991
02:12:20,480 --> 02:12:21,360
i

2992
02:12:21,360 --> 02:12:23,760
my

2993
02:12:23,760 --> 02:12:24,639
my observation on the curvature was

2994
02:12:24,639 --> 02:12:26,960
that

2995
02:12:26,960 --> 02:12:29,599
when the curvature is constant we have

2996
02:12:29,599 --> 02:12:32,239
some some tools to to compute on the

2997
02:12:32,239 --> 02:12:35,119
manifold and we have nice results of

2998
02:12:35,119 --> 02:12:36,800
existence and consistency etc

2999
02:12:36,800 --> 02:12:37,840
um

3000
02:12:37,840 --> 02:12:40,400
so

3001
02:12:40,400 --> 02:12:43,199
every time i see a non-constant sign

3002
02:12:43,199 --> 02:12:45,679
curvature i am a bit uh afraid of this

3003
02:12:45,679 --> 02:12:49,840
geometry then

3004
02:12:49,840 --> 02:12:52,719
the the the point is also that um

3005
02:12:52,719 --> 02:12:55,840
the the geometry should uh fit the

3006
02:12:55,840 --> 02:12:59,199
application and so

3007
02:12:59,199 --> 02:13:03,119
if for certain for certain reasons in in

3008
02:13:03,119 --> 02:13:06,560
an application we want to to

3009
02:13:06,560 --> 02:13:08,960
to be able to compute uh i don't know

3010
02:13:08,960 --> 02:13:10,719
i don't know why but to have a negative

3011
02:13:10,719 --> 02:13:11,760
curvature somewhere positive curvature

3012
02:13:11,760 --> 02:13:14,079
elsewhere

3013
02:13:14,079 --> 02:13:15,920
it could be interesting but the the

3014
02:13:15,920 --> 02:13:17,599
other points for example for the portion

3015
02:13:17,599 --> 02:13:19,040
to find the tree is that it is very

3016
02:13:19,040 --> 02:13:20,000
difficult to

3017
02:13:20,000 --> 02:13:22,800
to see

3018
02:13:22,800 --> 02:13:25,599
where uh is the negative curvature where

3019
02:13:25,599 --> 02:13:26,719
is the positive curvature i was able to

3020
02:13:26,719 --> 02:13:28,400
to

3021
02:13:28,400 --> 02:13:31,280
to to show that

3022
02:13:31,280 --> 02:13:33,360
in some directions it goes to infinity

3023
02:13:33,360 --> 02:13:35,360
but actually it was quite difficult to

3024
02:13:35,360 --> 02:13:36,719
find this direction so

3025
02:13:36,719 --> 02:13:37,840
uh

3026
02:13:37,840 --> 02:13:40,239
yeah

3027
02:13:40,239 --> 02:13:42,840
i would say that uh it's a bit dangerous

3028
02:13:42,840 --> 02:13:46,079
but if you want to go there why

3029
02:13:46,079 --> 02:13:49,599
not okay thank you very much

3030
02:13:49,599 --> 02:13:52,560
so i will give the final words to um the

3031
02:13:52,560 --> 02:13:54,560
advisor so gavier if you want to

3032
02:13:54,560 --> 02:13:57,280
maybe come forward and

3033
02:13:57,280 --> 02:14:00,639
ask your questions or

3034
02:14:00,639 --> 02:14:03,440
yeah thank you very much mr president

3035
02:14:03,440 --> 02:14:06,000
uh so yeah after all this time i think i

3036
02:14:06,000 --> 02:14:09,920
don't have questions i just have a few

3037
02:14:09,920 --> 02:14:11,599
uh things maybe to to say and to stress

3038
02:14:11,599 --> 02:14:13,199
first i want to start with a bit of

3039
02:14:13,199 --> 02:14:16,639
history

3040
02:14:16,639 --> 02:14:18,159
we were talking at lunch that we met

3041
02:14:18,159 --> 02:14:21,440
five years ago

3042
02:14:21,440 --> 02:14:23,280
uh during your gap year

3043
02:14:23,280 --> 02:14:27,599
and i remember that at that time you

3044
02:14:27,599 --> 02:14:29,760
were already deeply involved in teaching

3045
02:14:29,760 --> 02:14:32,159
not only mathematics to people at

3046
02:14:32,159 --> 02:14:35,840
programming mathematics but

3047
02:14:35,840 --> 02:14:35,840
organizing the courses at the course

3048
02:14:36,560 --> 02:14:40,639
at that time it was not very clear for

3049
02:14:40,639 --> 02:14:42,239
you that research will be

3050
02:14:42,239 --> 02:14:44,480
an interesting track

3051
02:14:44,480 --> 02:14:47,119
and after a year and a half of

3052
02:14:47,119 --> 02:14:50,159
discussion i'm glad i was glad to that

3053
02:14:50,159 --> 02:14:52,800
she decided finally to make a phd

3054
02:14:52,800 --> 02:14:54,800
and now i'm even more

3055
02:14:54,800 --> 02:14:55,520
impressed because i had the impression

3056
02:14:55,520 --> 02:14:57,840
to

3057
02:14:57,840 --> 02:15:01,360
throw away a few seeds for your research

3058
02:15:01,360 --> 02:15:03,040
and then now you grew out of that a real

3059
02:15:03,040 --> 02:15:05,119
forest where

3060
02:15:05,119 --> 02:15:07,199
maybe you are the only one

3061
02:15:07,199 --> 02:15:09,440
to know the whole thing and

3062
02:15:09,440 --> 02:15:12,079
i have to admit that scientifically i

3063
02:15:12,079 --> 02:15:13,679
can't say anything you know

3064
02:15:13,679 --> 02:15:16,320
you have a view which is much deeper

3065
02:15:16,320 --> 02:15:18,320
than um much deeper and much wider than

3066
02:15:18,320 --> 02:15:20,639
the one i have now on this

3067
02:15:20,639 --> 02:15:21,360
on this topic

3068
02:15:21,360 --> 02:15:24,159
so

3069
02:15:24,159 --> 02:15:26,400
maybe just a few other points uh that i

3070
02:15:26,400 --> 02:15:28,320
want to stress one of them is that when

3071
02:15:28,320 --> 02:15:32,639
you arrive this was corresponding to the

3072
02:15:32,639 --> 02:15:35,679
start of uh the g statistics dlc project

3073
02:15:35,679 --> 02:15:36,490
and you were already instrumental in

3074
02:15:36,490 --> 02:15:37,760
actually

3075
02:15:37,760 --> 02:15:41,119
[Music]

3076
02:15:41,119 --> 02:15:42,480
building and creating the team spirit

3077
02:15:42,480 --> 02:15:44,719
uh

3078
02:15:44,719 --> 02:15:46,960
by discussing with everyone welcoming

3079
02:15:46,960 --> 02:15:49,360
everyone i think everybody in the team

3080
02:15:49,360 --> 02:15:51,840
was welcomed by you and

3081
02:15:51,840 --> 02:15:54,320
felt uh nicely

3082
02:15:54,320 --> 02:15:56,719
nicely welcomed so that's that's really

3083
02:15:56,719 --> 02:15:59,040
uh something uh

3084
02:15:59,040 --> 02:16:00,880
something very important for which i

3085
02:16:00,880 --> 02:16:03,520
want to thank you

3086
02:16:03,520 --> 02:16:04,400
uh your human qualities uh made it

3087
02:16:04,400 --> 02:16:07,119
really

3088
02:16:07,119 --> 02:16:09,040
uh a dream to collaborate with you and i

3089
02:16:09,040 --> 02:16:12,000
want to say that it's really in that way

3090
02:16:12,000 --> 02:16:13,679
that i i'm seeing it it's not

3091
02:16:13,679 --> 02:16:14,560
the other way

3092
02:16:14,560 --> 02:16:17,040
uh

3093
02:16:17,040 --> 02:16:19,920
yeah so in in conclusion i want to say

3094
02:16:19,920 --> 02:16:22,239
well that i really like the time we

3095
02:16:22,239 --> 02:16:23,280
spend together i hope you liked it as

3096
02:16:23,280 --> 02:16:25,440
well

3097
02:16:25,440 --> 02:16:27,679
uh and i would like to wish you a

3098
02:16:27,679 --> 02:16:29,599
brilliant journey for

3099
02:16:29,599 --> 02:16:32,079
your future academic

3100
02:16:32,079 --> 02:16:32,079
career

3101
02:16:35,280 --> 02:16:40,960
thank you very much

3102
02:16:40,960 --> 02:16:43,599
um so jana i i think that now the jury

3103
02:16:43,599 --> 02:16:45,760
will want to um have a private

3104
02:16:45,760 --> 02:16:47,200
discussion so i i would suggest that we

3105
02:16:47,200 --> 02:16:49,679
stop the

3106
02:16:49,679 --> 02:16:51,359
live streaming um yes

3107
02:16:51,359 --> 02:16:53,599
and then maybe i will ask you to leave

3108
02:16:53,599 --> 02:16:55,439
the room and and

3109
02:16:55,439 --> 02:16:56,399
i suppose there will be a way to hold

3110
02:16:56,399 --> 02:16:58,960
you back

3111
02:16:58,960 --> 02:17:01,120
so just one word for people online

3112
02:17:01,120 --> 02:17:04,880
we are going to

3113
02:17:04,880 --> 02:17:04,880
to come back in a while uh

3114
02:17:18,399 --> 02:17:21,399
yes

3115
02:17:37,040 --> 02:17:40,040
uh

3116
02:17:43,280 --> 02:17:46,280
um

3117
02:17:49,799 --> 02:17:52,889
[Music]

3118
02:17:58,160 --> 02:18:01,559
digital medicine

3119
02:18:22,479 --> 02:18:25,479
um

3120
02:18:31,920 --> 02:18:35,439
okay excellent

3121
02:18:35,439 --> 02:18:39,280
um

3122
02:18:39,280 --> 02:18:41,760
so i will just read the reports that the

3123
02:18:41,760 --> 02:18:43,519
jury wrote after your

3124
02:18:43,519 --> 02:18:44,880
presentations and your answers to the

3125
02:18:44,880 --> 02:18:46,000
questions

3126
02:18:46,000 --> 02:18:48,240
um

3127
02:18:48,240 --> 02:18:51,040
so we found that you gave a particularly

3128
02:18:51,040 --> 02:18:52,960
inspiring and comprehensive presentation

3129
02:18:52,960 --> 02:18:55,359
of your work

3130
02:18:55,359 --> 02:18:58,479
and that you succeeded to highlight the

3131
02:18:58,479 --> 02:19:00,639
main contribution of the dissertation

3132
02:19:00,639 --> 02:19:02,880
in a style that struck a remarkable

3133
02:19:02,880 --> 02:19:05,519
balance between pedagogy and

3134
02:19:05,519 --> 02:19:07,359
mathematical rigor

3135
02:19:07,359 --> 02:19:10,000
we found that your presentation was very

3136
02:19:10,000 --> 02:19:13,200
well structured and were impressed by

3137
02:19:13,200 --> 02:19:16,000
the quality of the slides that contained

3138
02:19:16,000 --> 02:19:18,319
very insightful illustrations

3139
02:19:18,319 --> 02:19:21,359
and you were able to convey

3140
02:19:21,359 --> 02:19:23,599
quite abstract geometric concept um

3141
02:19:23,599 --> 02:19:25,920
to a general audience

3142
02:19:25,920 --> 02:19:27,280
which is certainly not easy

3143
02:19:27,280 --> 02:19:29,760
um

3144
02:19:29,760 --> 02:19:32,240
the answer to the questions demonstrated

3145
02:19:32,240 --> 02:19:35,359
your deep understanding of the concepts

3146
02:19:35,359 --> 02:19:38,639
and and also your ability to relate your

3147
02:19:38,639 --> 02:19:41,280
own work to the bigger picture

3148
02:19:41,280 --> 02:19:44,080
so overall the jury was unanimously

3149
02:19:44,080 --> 02:19:45,280
impressed by the quantity and the

3150
02:19:45,280 --> 02:19:46,840
quality

3151
02:19:46,840 --> 02:19:49,920
of the novel

3152
02:19:49,920 --> 02:19:52,399
results think that the dissertation is

3153
02:19:52,399 --> 02:19:54,000
likely to become a major reference on

3154
02:19:54,000 --> 02:19:56,399
the geometry of covariance and

3155
02:19:56,399 --> 02:19:58,080
correlation matrices

3156
02:19:58,080 --> 02:20:00,960
it paves the way to further

3157
02:20:00,960 --> 02:20:03,840
investigations of both theoretical and

3158
02:20:03,840 --> 02:20:03,840
practical nature

3159
02:20:04,479 --> 02:20:08,720
hence the results the presentation and

3160
02:20:08,720 --> 02:20:11,439
the answer to the questions all suggest

3161
02:20:11,439 --> 02:20:14,240
that you are highly promising

3162
02:20:14,240 --> 02:20:15,760
researchers with excellent pedagogical

3163
02:20:15,760 --> 02:20:17,680
skills

3164
02:20:17,680 --> 02:20:20,240
and for all these reasons

3165
02:20:20,240 --> 02:20:23,760
the jury wants to award you the title of

3166
02:20:23,760 --> 02:20:25,520
a doctor of university called dazur

3167
02:20:25,520 --> 02:20:27,439
and we all wish to sincerely

3168
02:20:27,439 --> 02:20:31,399
congratulate you

3169
02:20:31,399 --> 02:20:31,399
and so a bigger close

3170
02:20:34,640 --> 02:20:40,720
only sorry not to be there in person to

3171
02:20:40,720 --> 02:20:40,720
shake your hands

3172
02:20:42,000 --> 02:20:45,280
sorry older we couldn't hear what you

3173
02:20:45,280 --> 02:20:47,520
said

3174
02:20:47,520 --> 02:20:50,960
in the end i i'm only sorry that i'm not

3175
02:20:50,960 --> 02:20:52,479
not there in person to shake hands

3176
02:20:52,479 --> 02:20:54,720
yeah thank you

3177
02:20:54,720 --> 02:20:54,970
uh thank you very much for you for the

3178
02:20:54,970 --> 02:20:56,560
the

3179
02:20:56,560 --> 02:20:59,920
[Music]

3180
02:20:59,920 --> 02:21:03,439
that's the most difficult part probably

3181
02:21:03,439 --> 02:21:06,399
so uh yeah i would like to thank all the

3182
02:21:06,399 --> 02:21:08,880
jury members uh and so rodol for being

3183
02:21:08,880 --> 02:21:13,200
the the president of the jury

3184
02:21:13,200 --> 02:21:15,439
uh mark and and kieran juan for your

3185
02:21:15,439 --> 02:21:18,240
uh very interesting comments in the in

3186
02:21:18,240 --> 02:21:19,600
the reports uh i'm sure it will help me

3187
02:21:19,600 --> 02:21:22,800
improve

3188
02:21:22,800 --> 02:21:23,920
the the manuscript

3189
02:21:23,920 --> 02:21:28,080
again

3190
02:21:28,080 --> 02:21:28,080
uh i would like to thank also uh

3191
02:21:28,399 --> 02:21:34,399
especially uh for for coming also and

3192
02:21:34,399 --> 02:21:35,840
for your uh very interesting questions

3193
02:21:35,840 --> 02:21:37,840
and i think uh

3194
02:21:37,840 --> 02:21:39,120
our nice

3195
02:21:39,120 --> 02:21:40,479
discussion

3196
02:21:40,479 --> 02:21:42,240
um

3197
02:21:42,240 --> 02:21:43,359
i would like to thank

3198
02:21:43,359 --> 02:21:47,280
min

3199
02:21:47,280 --> 02:21:49,439
and and funk for also the the nice

3200
02:21:49,439 --> 02:21:52,240
discussions uh

3201
02:21:52,240 --> 02:21:54,319
and yeah maybe

3202
02:21:54,319 --> 02:21:56,560
funk i would like to thank you uh

3203
02:21:56,560 --> 02:21:58,240
particularly because you you followed me

3204
02:21:58,240 --> 02:22:01,600
uh during all the

3205
02:22:01,600 --> 02:22:02,960
the the thesis and gave me many uh uh

3206
02:22:02,960 --> 02:22:05,600
references and

3207
02:22:05,600 --> 02:22:06,479
advice so thank you

3208
02:22:06,479 --> 02:22:09,760
um

3209
02:22:09,760 --> 02:22:11,600
i would like to thank uh my supervisor

3210
02:22:11,600 --> 02:22:14,000
exactly uh

3211
02:22:14,000 --> 02:22:17,040
so i'm not sure i'm able to do it in

3212
02:22:17,040 --> 02:22:21,720
english so i will turn to french to be

3213
02:22:21,720 --> 02:22:21,720
able to express my gratitude

3214
02:22:24,240 --> 02:22:27,319
[Music]

3215
02:22:30,990 --> 02:22:34,069
[Music]

3216
02:22:42,020 --> 02:22:52,049
[Music]

3217
02:22:59,210 --> 02:23:02,320
[Music]

3218
02:23:07,960 --> 02:23:11,000
[Music]

3219
02:23:19,990 --> 02:23:28,149
[Music]

3220
02:23:29,760 --> 02:23:32,760
foreign

3221
02:23:49,690 --> 02:23:52,739
[Music]

3222
02:23:58,800 --> 02:24:01,800
uh

3223
02:24:06,399 --> 02:24:09,399
um

3224
02:24:13,100 --> 02:24:16,229
[Music]

3225
02:24:22,890 --> 02:24:27,240
[Music]

3226
02:24:27,240 --> 02:24:27,240
foreign

3227
02:24:28,640 --> 02:24:31,700
[Music]

3228
02:24:47,270 --> 02:24:50,399
[Music]

3229
02:24:53,200 --> 02:24:56,200
um

3230
02:25:11,650 --> 02:25:20,350
[Music]

3231
02:25:31,600 --> 02:25:34,600
um

3232
02:25:35,030 --> 02:25:38,479
[Music]

3233
02:25:50,880 --> 02:25:53,840
foreign

3234
02:26:00,400 --> 02:26:03,709
[Music]

3235
02:26:05,890 --> 02:26:14,540
[Music]

3236
02:26:17,760 --> 02:26:20,760
um

3237
02:26:21,810 --> 02:26:24,870
[Music]

3238
02:26:32,240 --> 02:26:35,240
um

3239
02:26:45,640 --> 02:26:49,800
[Music]

3240
02:26:49,800 --> 02:26:49,800
foreign

3241
02:26:52,140 --> 02:26:58,280
[Music]

3242
02:26:59,760 --> 02:27:02,760
is

3243
02:27:08,630 --> 02:27:11,870
[Music]

3244
02:27:16,080 --> 02:27:19,080
foreign

3245
02:27:20,530 --> 02:27:28,459
[Music]

3246
02:27:34,140 --> 02:27:37,330
[Music]

3247
02:27:40,479 --> 02:27:43,479
um

3248
02:27:48,690 --> 02:27:51,860
[Music]

3249
02:28:06,720 --> 02:28:09,720
foreign

3250
02:28:24,319 --> 02:28:27,319
um

3251
02:28:36,960 --> 02:28:39,960
um

3252
02:28:41,130 --> 02:28:48,519
[Music]

3253
02:28:50,990 --> 02:28:54,020
[Music]

3254
02:28:55,439 --> 02:28:58,439
um

3255
02:29:17,190 --> 02:29:20,259
[Music]

3256
02:29:31,840 --> 02:29:34,840
um

3257
02:29:40,910 --> 02:29:44,319
[Applause]

3258
02:29:54,960 --> 02:30:01,200
thank you very much so we will close the

3259
02:30:01,200 --> 02:30:02,479
zoom and we wish you a happy celebration

3260
02:30:02,479 --> 02:30:05,200
thank you

3261
02:30:05,200 --> 02:30:07,840
and i hope to meet you

3262
02:30:07,840 --> 02:30:09,040
at some point elsewhere

3263
02:30:09,040 --> 02:30:11,439
definitely

3264
02:30:11,439 --> 02:30:11,439
thank you

3265
02:30:12,000 --> 02:30:15,280
okay

3266
02:30:15,280 --> 02:30:18,280
thank you

3267
02:30:18,280 --> 02:30:18,280
goodbye

3268
02:30:20,840 --> 02:30:25,150
[Laughter]